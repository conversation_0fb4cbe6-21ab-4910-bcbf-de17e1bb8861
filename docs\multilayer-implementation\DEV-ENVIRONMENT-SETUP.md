# Development Environment Setup for AI Agents

## Overview

This document provides step-by-step setup instructions specifically for AI agents implementing the multi-material print job system. It includes all necessary tools, configurations, and validation steps.

## Prerequisites Validation

### System Requirements
```bash
# Verify Python version (3.9+ required)
python --version
# Should output: Python 3.9.x or higher

# Verify Node.js version (16+ required)  
node --version
# Should output: v16.x.x or higher

# Verify npm version
npm --version
# Should output: 8.x.x or higher
```

### Required Tools Installation

#### Windows Development Setup
```powershell
# Install Python dependencies
cd backend
pip install -r requirements.txt

# Install additional dependencies for multi-material implementation
pip install asyncua>=1.0.0
pip install pytest-mock>=3.12.0

# Install Node.js dependencies
cd ../frontend
npm install

# Verify installations
cd ../backend
python -c "import fastapi, asyncua; print('Backend dependencies OK')"
cd ../frontend
npm run build --dry-run
```

#### Linux/Mac Development Setup
```bash
# Create virtual environment (recommended)
cd backend
python -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install asyncua>=1.0.0

# Install frontend dependencies
cd ../frontend
npm install

# Verify setup
cd ../backend
python -c "import fastapi, asyncua; print('Backend dependencies OK')"
```

## Development Environment Configuration

### Backend Configuration

#### Environment Variables (.env file)
```env
# Application Settings
DEBUG=true
ENVIRONMENT=development
LOG_LEVEL=DEBUG

# Recoater Hardware Settings
RECOATER_BASE_URL=http://*************:8080
RECOATER_TIMEOUT=5.0

# OPC UA Configuration (for multi-material implementation)
OPCUA_ENDPOINT=opc.tcp://localhost:4843
OPCUA_NAMESPACE=2
OPCUA_TIMEOUT=5.0
OPCUA_SECURITY_POLICY=None
OPCUA_SECURITY_MODE=None

# Multi-Material Job Settings
MAX_CONCURRENT_JOBS=1
LAYER_UPLOAD_TIMEOUT=30.0
COORDINATION_TIMEOUT=60.0
DRUM_SYNC_TIMEOUT=10.0

# Development Settings
MOCK_HARDWARE=true
MOCK_OPCUA=true
```

#### Required Directory Structure
```
backend/
├── app/
│   ├── config/           # Create this directory
│   ├── models/           # Create this directory  
│   ├── services/
│   │   ├── opcua_coordinator.py      # To be implemented
│   │   ├── coordination_engine.py    # To be implemented
│   │   └── multilayer_job_manager.py # To be implemented
│   └── api/
└── tests/
    ├── unit/             # Create this directory
    ├── integration/      # Create this directory
    └── mocks/            # Create this directory
```

### Frontend Configuration

#### Development Server Settings (vite.config.js)
```javascript
export default defineConfig({
  plugins: [vue()],
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      },
      '/ws': {
        target: 'ws://localhost:8000',
        ws: true
      }
    }
  },
  // Add for multi-material development
  define: {
    __MULTI_MATERIAL_ENABLED__: true,
    __MAX_DRUMS__: 3
  }
})
```

## Mock Services Setup

### Mock OPC UA Server (for Stage 1 Development)

#### Installation
```bash
# Install OPC UA test server
npm install -g node-opcua-server

# Or use Docker
docker run -p 4843:4843 opcua/opcua-server:latest
```

#### Mock OPC UA Configuration
Create `dev-tools/mock-opcua-server.py`:
```python
"""
Mock OPC UA Server for Development
Simulates TwinCAT PLC variables for multi-material coordination
"""

import asyncio
import logging
from asyncua import Server, ua

async def init_mock_opcua_server():
    server = Server()
    await server.init()
    server.set_endpoint("opc.tcp://localhost:4843/freeopcua/server/")
    
    # Setup namespace
    uri = "http://recoater.mock.server"
    idx = await server.register_namespace(uri)
    
    # Create variable nodes for multi-material coordination
    objects = server.get_objects_node()
    
    # Job control variables
    job_active = await objects.add_variable(idx, "job_active", False)
    total_layers = await objects.add_variable(idx, "total_layers", 0)
    current_layer = await objects.add_variable(idx, "current_layer", 0)
    
    # Recoater coordination variables
    recoater_ready_to_print = await objects.add_variable(idx, "recoater_ready_to_print", False)
    recoater_start_signal = await objects.add_variable(idx, "recoater_start_signal", False)
    recoater_layer_complete = await objects.add_variable(idx, "recoater_layer_complete", False)
    
    # Galvo coordination variables
    galvo_ready_to_scan = await objects.add_variable(idx, "galvo_ready_to_scan", False)
    galvo_scan_complete = await objects.add_variable(idx, "galvo_scan_complete", False)
    
    # System status variables
    system_error = await objects.add_variable(idx, "system_error", False)
    error_message = await objects.add_variable(idx, "error_message", "")
    
    # Make variables writable
    for var in [job_active, total_layers, current_layer, recoater_ready_to_print,
                recoater_start_signal, recoater_layer_complete, galvo_ready_to_scan,
                galvo_scan_complete, system_error, error_message]:
        await var.set_writable()
    
    return server

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    async def main():
        server = await init_mock_opcua_server()
        async with server:
            print("Mock OPC UA Server started at opc.tcp://localhost:4843")
            print("Press Ctrl+C to stop")
            await asyncio.Event().wait()
    
    asyncio.run(main())
```

### Enhanced Mock Recoater (for Multi-Material Testing)

Extend the existing `backend/services/mock_recoater_client.py`:
```python
# Add to existing MockRecoaterClient class

def __init__(self):
    # ... existing initialization ...
    
    # Add multi-material state tracking
    self._drum_states = {0: "ready", 1: "ready", 2: "ready"}
    self._current_layers = {0: 0, 1: 0, 2: 0}
    self._job_active = False
    
def get_multimaterial_status(self) -> Dict[str, Any]:
    """Get status for all drums in multi-material operation"""
    return {
        "job_active": self._job_active,
        "drums": {
            str(drum_id): {
                "status": state,
                "current_layer": self._current_layers[drum_id]
            }
            for drum_id, state in self._drum_states.items()
        }
    }

def start_multimaterial_job(self, total_layers: int) -> Dict[str, Any]:
    """Start a multi-material job across all drums"""
    self._job_active = True
    self._total_layers = total_layers
    for drum_id in self._drum_states:
        self._current_layers[drum_id] = 1
        self._drum_states[drum_id] = "ready"
    
    return {"success": True, "job_id": f"multi_{int(time.time())}"}
```

## Development Workflow

### Daily Development Setup
```bash
# 1. Start mock services (in separate terminals)
cd dev-tools
python mock-opcua-server.py

# 2. Start backend with development settings
cd backend
python -m uvicorn app.main:app --reload --log-level debug

# 3. Start frontend development server
cd frontend
npm run dev

# 4. Verify all services are running
curl http://localhost:8000/api/v1/status
curl http://localhost:5173
```

### Testing Setup
```bash
# Backend testing
cd backend
pytest tests/ -v --asyncio-mode=auto

# Frontend testing  
cd frontend
npm run test

# Integration testing (requires mock services)
cd backend
pytest tests/integration/ -v --asyncio-mode=auto
```

## Validation Checklist

### Environment Validation
- [ ] Python 3.9+ installed and accessible
- [ ] Node.js 16+ installed and accessible
- [ ] All Python dependencies installed without errors
- [ ] All Node.js dependencies installed without errors
- [ ] Backend starts without errors on port 8000
- [ ] Frontend starts without errors on port 5173
- [ ] Mock OPC UA server accessible on port 4843
- [ ] Mock recoater client responds correctly

### Development Tools Validation
- [ ] VS Code or preferred IDE configured
- [ ] Python debugger working with FastAPI
- [ ] Vue.js devtools extension installed
- [ ] Git configured for version control
- [ ] Environment variables loaded correctly

### Multi-Material Specific Validation
- [ ] OPC UA client can connect to mock server
- [ ] Multi-drum API endpoints accessible
- [ ] 3-file upload validation working
- [ ] Frontend components render without errors
- [ ] WebSocket connections established
- [ ] Error handling patterns working

## Troubleshooting Common Issues

### Backend Issues
```bash
```bash
# OPC UA connection errors
# Check if mock server is running on port 4843
netstat -an | grep 4843

# Import errors for asyncua
pip install asyncua>=1.0.0
```

# FastAPI startup errors
# Check .env file exists and has correct values
cat backend/.env
```

### Frontend Issues
```bash
# Node modules issues
cd frontend
rm -rf node_modules package-lock.json
npm install

# Vite proxy errors
# Verify backend is running on port 8000
curl http://localhost:8000/api/v1/status
```

### Integration Issues
```bash
# WebSocket connection failures
# Check CORS settings in backend/app/main.py
# Verify WebSocket endpoint is accessible

# API communication errors
# Check network connectivity between frontend and backend
# Verify API endpoints are correctly defined
```

This development environment setup ensures AI agents have all necessary tools and configurations to implement the multi-material print job system effectively.
