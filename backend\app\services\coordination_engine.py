"""
Multi-Material Coordination Engine
==================================

This service implements the core coordination logic for multi-material print jobs
with 3-drum coordination. It orchestrates between Aerosint server, PLC, and backend
for automated layer progression across all drums.

Based on Stage 3 requirements from the implementation documentation.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum

from app.models.multilayer_job import MultiMaterialJobState, JobStatus, DrumState
from services.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError
from app.services.opcua_coordinator import opcua_coordinator
from app.services.multilayer_job_manager import MultiMaterialJobManager

logger = logging.getLogger(__name__)


class CoordinationState(Enum):
    """States for the coordination engine"""
    IDLE = "idle"
    UPLOADING = "uploading"
    WAITING_FOR_READY = "waiting_for_ready"
    PRINTING = "printing"
    WAITING_FOR_COMPLETION = "waiting_for_completion"
    ERROR = "error"
    COMPLETE = "complete"


class CoordinationError(Exception):
    """Raised when coordination operations fail."""
    pass


class MultiMaterialCoordinationEngine:
    """
    Manages multi-material print job coordination with 3-drum system.
    
    This engine handles:
    - Multi-drum layer upload coordination
    - Aerosint status monitoring for all drums
    - Backend-driven layer progression
    - Error handling with backend_error and plc_error flags
    - Simplified coordination with minimal PLC dependency
    """
    
    def __init__(self, recoater_client: RecoaterClient, job_manager: MultiMaterialJobManager):
        """
        Initialize the coordination engine.
        
        Args:
            recoater_client: Client for communicating with recoater hardware
            job_manager: Manager for multi-material job state
        """
        self.recoater_client = recoater_client
        self.job_manager = job_manager
        
        # Coordination state
        self.state = CoordinationState.IDLE
        self.current_job: Optional[MultiMaterialJobState] = None
        
        # Timing configuration
        self.drum_upload_delay = 2.0  # 2-second delay between drum uploads
        self.status_poll_interval = 1.0  # Poll Aerosint status every second
        self.ready_timeout = 30.0  # Timeout for drums to become ready
        self.completion_timeout = 300.0  # Timeout for layer completion
        
        # Error tracking
        self.error_count = 0
        self.max_retries = 3
        
        logger.info("MultiMaterialCoordinationEngine initialized")
    
    async def start_multimaterial_job(self, job_state: MultiMaterialJobState) -> bool:
        """
        Start a multi-material job with 3-drum coordination.
        
        Args:
            job_state: The job state to coordinate
            
        Returns:
            bool: True if job started successfully
            
        Raises:
            CoordinationError: If job start fails
        """
        if self.state != CoordinationState.IDLE:
            raise CoordinationError(f"Cannot start job: engine in state {self.state}")
        
        try:
            self.current_job = job_state
            self.state = CoordinationState.UPLOADING
            self.error_count = 0
            
            logger.info(f"Starting multi-material job {job_state.job_id} with {job_state.total_layers} layers")
            
            # Update OPC UA variables
            if opcua_coordinator:
                await opcua_coordinator.set_job_active(job_state.total_layers)
                await opcua_coordinator.update_layer_progress(job_state.current_layer)
                await opcua_coordinator.clear_error_flags()
            
            # Start the coordination process
            asyncio.create_task(self._coordination_loop())
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start multi-material job: {e}")
            self.state = CoordinationState.ERROR
            await self._set_error_state(f"Job start failed: {e}")
            raise CoordinationError(f"Job start failed: {e}")
    
    async def stop_job(self) -> bool:
        """
        Stop the current multi-material job.
        
        Returns:
            bool: True if job stopped successfully
        """
        if self.current_job:
            logger.info(f"Stopping multi-material job {self.current_job.job_id}")
            self.current_job.is_active = False
            self.current_job.status = JobStatus.CANCELLED
        
        self.state = CoordinationState.IDLE
        self.current_job = None
        
        # Update OPC UA variables
        if opcua_coordinator:
            await opcua_coordinator.set_job_inactive()
        
        return True
    
    async def _coordination_loop(self) -> None:
        """
        Main coordination loop for multi-material job execution.
        """
        try:
            while (self.current_job and 
                   self.current_job.is_active and 
                   self.current_job.current_layer <= self.current_job.total_layers):
                
                logger.info(f"Processing layer {self.current_job.current_layer}/{self.current_job.total_layers}")
                
                # Process current layer across all drums
                success = await self._process_layer_cycle_all_drums()
                
                if not success:
                    logger.error(f"Layer {self.current_job.current_layer} processing failed")
                    break
                
                # Advance to next layer
                self.current_job.current_layer += 1
                
                # Update progress
                if opcua_coordinator:
                    await opcua_coordinator.update_layer_progress(self.current_job.current_layer)
            
            # Job completed successfully
            if (self.current_job and 
                self.current_job.current_layer > self.current_job.total_layers):
                await self._complete_job()
            
        except Exception as e:
            logger.error(f"Coordination loop error: {e}")
            await self._set_error_state(f"Coordination error: {e}")
    
    async def _process_layer_cycle_all_drums(self) -> bool:
        """
        Process a complete layer cycle for all 3 drums.
        
        Returns:
            bool: True if layer cycle completed successfully
        """
        try:
            # Step 1: Upload layer to all drums
            self.state = CoordinationState.UPLOADING
            upload_success = await self._upload_layer_to_all_drums()
            
            if not upload_success:
                return False
            
            # Step 2: Wait for all drums to be ready
            self.state = CoordinationState.WAITING_FOR_READY
            ready_success = await self._wait_for_all_drums_ready()
            
            if not ready_success:
                return False
            
            # Step 3: Signal PLC that recoater is ready
            if opcua_coordinator:
                await opcua_coordinator.set_recoater_ready_to_print(True)
            
            # Step 4: Wait for layer completion
            self.state = CoordinationState.WAITING_FOR_COMPLETION
            completion_success = await self._wait_for_layer_completion()
            
            if not completion_success:
                return False
            
            # Step 5: Signal layer complete
            if opcua_coordinator:
                await opcua_coordinator.set_recoater_layer_complete(True)
                await asyncio.sleep(1)  # Brief delay
                await opcua_coordinator.set_recoater_layer_complete(False)
                await opcua_coordinator.set_recoater_ready_to_print(False)
            
            return True
            
        except Exception as e:
            logger.error(f"Layer cycle error: {e}")
            await self._set_error_state(f"Layer cycle failed: {e}")
            return False
    
    async def _upload_layer_to_all_drums(self) -> bool:
        """
        Upload current layer to all 3 drums with coordination delay.
        
        Returns:
            bool: True if all uploads successful
        """
        if not self.current_job:
            return False
        
        try:
            layer_num = self.current_job.current_layer
            upload_results = {}
            
            # Upload to each drum with delay
            for drum_id in [0, 1, 2]:
                try:
                    # Get layer data for this drum
                    if drum_id in self.current_job.remaining_layers:
                        layer_data_list = self.current_job.remaining_layers[drum_id]
                        if layer_num <= len(layer_data_list):
                            layer_data = layer_data_list[layer_num - 1]  # Convert to 0-based

                            if not layer_data.is_empty:
                                # Upload layer data
                                await self._upload_cli_data_to_drum(drum_id, layer_data.cli_data)
                                upload_results[drum_id] = True
                                logger.info(f"Uploaded layer {layer_num} to drum {drum_id}")
                            else:
                                # Skip empty layer
                                upload_results[drum_id] = True
                                logger.info(f"Skipped empty layer {layer_num} for drum {drum_id}")
                        else:
                            # Drum depleted
                            upload_results[drum_id] = True
                            logger.info(f"Drum {drum_id} depleted at layer {layer_num}")
                    else:
                        # No layer data for this drum
                        upload_results[drum_id] = True
                        logger.info(f"No layer data for drum {drum_id}")

                    # Delay between uploads
                    if drum_id < 2:  # Don't delay after last drum
                        await asyncio.sleep(self.drum_upload_delay)
                        
                except Exception as e:
                    logger.error(f"Failed to upload to drum {drum_id}: {e}")
                    upload_results[drum_id] = False
            
            # Check if all uploads succeeded
            success = all(upload_results.values())
            
            if success:
                logger.info(f"Successfully uploaded layer {layer_num} to all drums")
            else:
                failed_drums = [drum_id for drum_id, result in upload_results.items() if not result]
                logger.error(f"Upload failed for drums: {failed_drums}")
            
            return success
            
        except Exception as e:
            logger.error(f"Multi-drum upload error: {e}")
            return False

    async def _upload_cli_data_to_drum(self, drum_id: int, cli_data: bytes) -> bool:
        """
        Upload CLI data to a specific drum.

        Args:
            drum_id: Target drum (0, 1, or 2)
            cli_data: ASCII CLI data to upload

        Returns:
            bool: True if upload successful
        """
        try:
            # Use the recoater client to upload CLI data
            await self.recoater_client.upload_cli_data(drum_id, cli_data)
            return True

        except (RecoaterConnectionError, RecoaterAPIError) as e:
            logger.error(f"Failed to upload CLI data to drum {drum_id}: {e}")
            return False

    async def _wait_for_all_drums_ready(self) -> bool:
        """
        Wait for all drums to reach ready state.

        Returns:
            bool: True if all drums ready within timeout
        """
        start_time = time.time()

        while time.time() - start_time < self.ready_timeout:
            try:
                # Check status of all drums
                all_ready = True
                drum_statuses = {}

                for drum_id in [0, 1, 2]:
                    try:
                        status = await self.recoater_client.get_drum_status(drum_id)
                        drum_statuses[drum_id] = status

                        # Check if drum is ready (status is a dict with 'ready' field)
                        if not status.get('ready', False):
                            all_ready = False

                    except Exception as e:
                        logger.warning(f"Failed to get status for drum {drum_id}: {e}")
                        all_ready = False

                if all_ready:
                    logger.info("All drums ready for printing")
                    return True

                # Wait before next poll
                await asyncio.sleep(self.status_poll_interval)

            except Exception as e:
                logger.error(f"Error checking drum readiness: {e}")
                await asyncio.sleep(self.status_poll_interval)

        logger.error(f"Timeout waiting for drums to be ready ({self.ready_timeout}s)")
        return False

    async def _wait_for_layer_completion(self) -> bool:
        """
        Wait for layer completion signal from PLC.

        Returns:
            bool: True if layer completed within timeout
        """
        start_time = time.time()

        while time.time() - start_time < self.completion_timeout:
            try:
                # Check for completion signal or error flags
                if opcua_coordinator:
                    # Check for backend or PLC errors
                    backend_error = await opcua_coordinator.get_backend_error()
                    plc_error = await opcua_coordinator.get_plc_error()

                    if backend_error or plc_error:
                        error_msg = f"Error during layer completion: backend_error={backend_error}, plc_error={plc_error}"
                        logger.error(error_msg)
                        await self._set_error_state(error_msg)
                        return False

                    # In simplified coordination, we assume layer is complete
                    # when sufficient time has passed or when PLC signals completion
                    # For now, we'll use a simple time-based approach
                    if time.time() - start_time > 5.0:  # Minimum 5 seconds
                        logger.info("Layer completion assumed (simplified coordination)")
                        return True

                await asyncio.sleep(self.status_poll_interval)

            except Exception as e:
                logger.error(f"Error waiting for layer completion: {e}")
                await asyncio.sleep(self.status_poll_interval)

        logger.error(f"Timeout waiting for layer completion ({self.completion_timeout}s)")
        return False

    async def _complete_job(self) -> None:
        """Complete the current job successfully."""
        if not self.current_job:
            return

        logger.info(f"Multi-material job {self.current_job.job_id} completed successfully")

        self.current_job.status = JobStatus.COMPLETED
        self.current_job.is_active = False
        self.state = CoordinationState.COMPLETE

        # Update OPC UA variables
        if opcua_coordinator:
            await opcua_coordinator.set_job_inactive()

        # Clean up
        self.current_job = None
        self.state = CoordinationState.IDLE

    async def _set_error_state(self, error_message: str) -> None:
        """
        Set the coordination engine to error state.

        Args:
            error_message: Description of the error
        """
        logger.error(f"Coordination engine error: {error_message}")

        self.state = CoordinationState.ERROR

        if self.current_job:
            self.current_job.status = JobStatus.ERROR
            self.current_job.is_active = False

        # Set backend error flag
        if opcua_coordinator:
            await opcua_coordinator.set_backend_error(True)

    async def clear_errors(self) -> bool:
        """
        Clear error state and reset coordination engine.

        Returns:
            bool: True if errors cleared successfully
        """
        try:
            logger.info("Clearing coordination engine errors")

            self.error_count = 0

            if self.state == CoordinationState.ERROR:
                self.state = CoordinationState.IDLE

            # Clear OPC UA error flags
            if opcua_coordinator:
                await opcua_coordinator.clear_error_flags()

            return True

        except Exception as e:
            logger.error(f"Failed to clear errors: {e}")
            return False

    def get_coordination_status(self) -> Dict[str, Any]:
        """
        Get current coordination engine status.

        Returns:
            dict: Status information
        """
        status = {
            "state": self.state.value,
            "error_count": self.error_count,
            "current_job": None
        }

        if self.current_job:
            status["current_job"] = {
                "job_id": self.current_job.job_id,
                "current_layer": self.current_job.current_layer,
                "total_layers": self.current_job.total_layers,
                "status": self.current_job.status.value,
                "is_active": self.current_job.is_active
            }

        return status
