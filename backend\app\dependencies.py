"""
Application Dependencies
========================

This module holds shared application dependencies to avoid circular imports.
It provides a centralized location for dependency injection patterns.
"""

import os
import logging
from typing import Optional
from fastapi import HTTPException

from services.recoater_client import Recoater<PERSON>lient
from services.mock_recoater_client import MockRecoater<PERSON>lient
from app.services.opcua_coordinator import opcua_coordinator

logger = logging.getLogger(__name__)

# Global recoater client instance
_recoater_client: Optional[RecoaterClient] = None

# Global OPC UA coordinator instance
_opcua_coordinator = None

# Global multilayer job manager instance
_multilayer_job_manager = None


def initialize_recoater_client() -> None:
    """
    Initialize the global recoater client instance.

    This should be called during application startup.
    """
    global _recoater_client

    base_url = os.getenv("RECOATER_API_BASE_URL", "http://172.16.17.224:8080")
    development_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"

    if development_mode:
        logger.info("Initializing mock recoater client for development mode")
        _recoater_client = MockRecoaterClient(base_url)
    else:
        logger.info("Initializing real recoater client for production mode")
        _recoater_client = RecoaterClient(base_url)


async def initialize_opcua_coordinator() -> None:
    """
    Initialize the global OPC UA coordinator instance.

    This should be called during application startup.
    """
    global _opcua_coordinator

    try:
        _opcua_coordinator = opcua_coordinator

        # Connect to OPC UA server
        connected = await _opcua_coordinator.connect()
        if connected:
            logger.info("OPC UA coordinator initialized and connected successfully")
        else:
            logger.warning("OPC UA coordinator initialized but connection failed")

    except Exception as e:
        logger.error(f"Failed to initialize OPC UA coordinator: {e}")
        _opcua_coordinator = None


def initialize_multilayer_job_manager() -> None:
    """
    Initialize the global multilayer job manager instance.

    This should be called during application startup after recoater client is initialized.
    """
    global _multilayer_job_manager

    try:
        from app.services.multilayer_job_manager import MultiMaterialJobManager

        if _recoater_client is None:
            raise RuntimeError("Recoater client must be initialized before multilayer job manager")

        _multilayer_job_manager = MultiMaterialJobManager(_recoater_client)
        logger.info("Multilayer job manager initialized successfully")

    except Exception as e:
        logger.error(f"Failed to initialize multilayer job manager: {e}")
        _multilayer_job_manager = None


def get_recoater_client() -> RecoaterClient:
    """
    Dependency function to get the recoater client instance.

    This function can be used with FastAPI's Depends() to inject
    the recoater client into API endpoints.

    Returns:
        RecoaterClient: The initialized recoater client instance

    Raises:
        HTTPException: If the recoater client is not initialized
    """
    if _recoater_client is None:
        logger.error("Recoater client not initialized")
        raise HTTPException(
            status_code=503,
            detail="Recoater client not initialized"
        )
    return _recoater_client


def get_opcua_coordinator():
    """
    Dependency function to get the OPC UA coordinator instance.

    This function can be used with FastAPI's Depends() to inject
    the OPC UA coordinator into API endpoints.

    Returns:
        OPCUACoordinator: The initialized OPC UA coordinator instance

    Raises:
        HTTPException: If the OPC UA coordinator is not initialized
    """
    if _opcua_coordinator is None:
        logger.error("OPC UA coordinator not initialized")
        raise HTTPException(
            status_code=503,
            detail="OPC UA coordinator not initialized"
        )
    return _opcua_coordinator


def get_multilayer_job_manager():
    """
    Dependency function to get the multilayer job manager instance.

    This function can be used with FastAPI's Depends() to inject
    the multilayer job manager into API endpoints.

    Returns:
        MultiMaterialJobManager: The initialized multilayer job manager instance

    Raises:
        HTTPException: If the multilayer job manager is not initialized
    """
    if _multilayer_job_manager is None:
        logger.error("Multilayer job manager not initialized")
        raise HTTPException(
            status_code=503,
            detail="Multilayer job manager not initialized"
        )
    return _multilayer_job_manager


def get_recoater_client_instance() -> Optional[RecoaterClient]:
    """
    Get the recoater client instance without raising exceptions.

    This is useful for internal application code that needs to access
    the client but doesn't want to handle HTTP exceptions.

    Returns:
        Optional[RecoaterClient]: The recoater client instance or None if not initialized
    """
    return _recoater_client
