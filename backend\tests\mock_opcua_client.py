"""
Mock OPC UA Client for Testing
==============================

Provides a mock OPC UA client to test server functionality without
requiring a real PLC connection.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Callable
from datetime import datetime

from asyncua import Client, ua

logger = logging.getLogger(__name__)


class MockOPCUAClient:
    """
    Mock OPC UA client for testing server functionality.
    
    This client can connect to the OPC UA server and simulate
    PLC behavior for testing coordination logic.
    """
    
    def __init__(self, server_endpoint: str = "opc.tcp://localhost:4843/recoater/server/"):
        """
        Initialize mock OPC UA client.
        
        Args:
            server_endpoint: OPC UA server endpoint to connect to
        """
        self.endpoint = server_endpoint
        self.client: Optional[Client] = None
        self.connected = False
        self.variable_nodes: Dict[str, Any] = {}
        self.subscription = None
        self._monitoring_task: Optional[asyncio.Task] = None
        self._change_handlers: Dict[str, Callable] = {}
        
        logger.info(f"Mock OPC UA client initialized for endpoint: {server_endpoint}")
    
    async def connect(self) -> bool:
        """
        Connect to the OPC UA server.
        
        Returns:
            bool: True if connection successful
        """
        try:
            if self.connected:
                logger.warning("Mock client already connected")
                return True
            
            self.client = Client(url=self.endpoint)
            await self.client.connect()
            
            # Discover coordination variables
            await self._discover_variables()
            
            self.connected = True
            logger.info(f"Mock client connected to {self.endpoint}")
            
            # Start monitoring task
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect mock client: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """
        Disconnect from the OPC UA server.
        
        Returns:
            bool: True if disconnection successful
        """
        try:
            self.connected = False
            
            # Cancel monitoring task
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
                try:
                    await self._monitoring_task
                except asyncio.CancelledError:
                    pass
            
            # Disconnect client
            if self.client:
                await self.client.disconnect()
                self.client = None
            
            self.variable_nodes.clear()
            logger.info("Mock client disconnected")
            return True
            
        except Exception as e:
            logger.error(f"Error disconnecting mock client: {e}")
            return False
    
    async def read_variable(self, variable_name: str) -> Any:
        """
        Read a variable value from the server.
        
        Args:
            variable_name: Name of the variable to read
            
        Returns:
            Any: Variable value or None if error
        """
        try:
            if not self.connected:
                logger.warning(f"Cannot read {variable_name}: not connected")
                return None
            
            if variable_name not in self.variable_nodes:
                logger.error(f"Variable {variable_name} not found")
                return None
            
            node = self.variable_nodes[variable_name]
            value = await node.read_value()
            
            logger.debug(f"Read {variable_name} = {value}")
            return value
            
        except Exception as e:
            logger.error(f"Failed to read variable {variable_name}: {e}")
            return None
    
    async def write_variable(self, variable_name: str, value: Any) -> bool:
        """
        Write a variable value to the server.
        
        Args:
            variable_name: Name of the variable to write
            value: Value to write
            
        Returns:
            bool: True if write successful
        """
        try:
            if not self.connected:
                logger.warning(f"Cannot write {variable_name}: not connected")
                return False
            
            if variable_name not in self.variable_nodes:
                logger.error(f"Variable {variable_name} not found")
                return False
            
            node = self.variable_nodes[variable_name]
            await node.write_value(value)
            
            logger.debug(f"Wrote {variable_name} = {value}")
            
            # Trigger change handler if registered
            if variable_name in self._change_handlers:
                try:
                    handler = self._change_handlers[variable_name]
                    if asyncio.iscoroutinefunction(handler):
                        await handler(variable_name, value)
                    else:
                        handler(variable_name, value)
                except Exception as e:
                    logger.error(f"Error in change handler for {variable_name}: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to write variable {variable_name}: {e}")
            return False
    
    async def subscribe_to_variable(self, variable_name: str, handler: Callable) -> bool:
        """
        Subscribe to variable changes.
        
        Args:
            variable_name: Name of the variable to monitor
            handler: Callback function for changes
            
        Returns:
            bool: True if subscription successful
        """
        try:
            self._change_handlers[variable_name] = handler
            logger.info(f"Subscribed to changes for {variable_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to subscribe to {variable_name}: {e}")
            return False
    
    async def simulate_plc_behavior(self) -> None:
        """
        Simulate typical PLC behavior for testing.
        
        This method simulates how a PLC would interact with
        the coordination variables during a print job.
        """
        try:
            logger.info("Starting PLC behavior simulation")
            
            # Wait for job to become active
            while self.connected:
                job_active = await self.read_variable("job_active")
                if job_active:
                    break
                await asyncio.sleep(1)
            
            if not self.connected:
                return
            
            logger.info("Job detected as active, starting PLC simulation")
            
            # Simulate layer processing cycle
            total_layers = await self.read_variable("total_layers")
            current_layer = 0
            
            while current_layer < total_layers and self.connected:
                current_layer += 1
                logger.info(f"PLC simulating layer {current_layer}/{total_layers}")
                
                # Wait for all drums to be ready
                await self._wait_for_all_drums_ready()
                
                # Signal print start
                await self.write_variable("print_start_signal", True)
                await asyncio.sleep(2)  # Simulate print time
                
                # Signal print complete
                await self.write_variable("print_complete_signal", True)
                await asyncio.sleep(1)  # Simulate galvo time
                
                # Signal galvo complete
                await self.write_variable("galvo_scan_complete", True)
                await asyncio.sleep(0.5)
                
                # Reset signals for next layer
                await self.write_variable("print_start_signal", False)
                await self.write_variable("print_complete_signal", False)
                await self.write_variable("galvo_scan_complete", False)
            
            logger.info("PLC simulation completed")
            
        except Exception as e:
            logger.error(f"Error in PLC simulation: {e}")
    
    async def _discover_variables(self) -> None:
        """Discover coordination variables from the server."""
        try:
            # Get coordination folder
            objects = self.client.get_objects_node()
            coordination_folder = await objects.get_child("2:MultiMaterialCoordination")
            
            # Get all variables in the folder
            children = await coordination_folder.get_children()
            
            for child in children:
                browse_name = await child.read_browse_name()
                var_name = browse_name.Name
                self.variable_nodes[var_name] = child
                logger.debug(f"Discovered variable: {var_name}")
            
            logger.info(f"Discovered {len(self.variable_nodes)} variables")
            
        except Exception as e:
            logger.error(f"Failed to discover variables: {e}")
    
    async def _wait_for_all_drums_ready(self) -> None:
        """Wait for all drums to be ready."""
        while self.connected:
            all_ready = await self.read_variable("all_drums_ready")
            if all_ready:
                break
            await asyncio.sleep(0.5)
    
    async def _monitoring_loop(self) -> None:
        """Monitor server variables for changes."""
        try:
            last_values = {}
            
            while self.connected:
                # Check for changes in key variables
                for var_name in ["job_active", "current_layer", "backend_error", "plc_error"]:
                    if var_name in self.variable_nodes:
                        current_value = await self.read_variable(var_name)
                        
                        if var_name not in last_values or last_values[var_name] != current_value:
                            last_values[var_name] = current_value
                            logger.debug(f"Variable change detected: {var_name} = {current_value}")
                
                await asyncio.sleep(1)
                
        except asyncio.CancelledError:
            logger.debug("Mock client monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Error in monitoring loop: {e}")
    
    def is_connected(self) -> bool:
        """Check if client is connected."""
        return self.connected
    
    def get_variable_count(self) -> int:
        """Get number of discovered variables."""
        return len(self.variable_nodes)
    
    def get_variable_names(self) -> list:
        """Get list of discovered variable names."""
        return list(self.variable_nodes.keys())


async def test_opcua_server_with_mock_client(server_endpoint: str = "opc.tcp://localhost:4843/recoater/server/"):
    """
    Test function to validate OPC UA server with mock client.
    
    Args:
        server_endpoint: Server endpoint to test
        
    Returns:
        bool: True if test successful
    """
    client = MockOPCUAClient(server_endpoint)
    
    try:
        # Connect to server
        connected = await client.connect()
        if not connected:
            logger.error("Failed to connect to OPC UA server")
            return False
        
        logger.info("Mock client connected successfully")
        
        # Test variable operations
        test_passed = True
        
        # Test reading initial values
        job_active = await client.read_variable("job_active")
        if job_active is None:
            logger.error("Failed to read job_active variable")
            test_passed = False
        
        # Test writing values
        write_success = await client.write_variable("job_active", True)
        if not write_success:
            logger.error("Failed to write job_active variable")
            test_passed = False
        
        # Test reading written value
        job_active_after = await client.read_variable("job_active")
        if job_active_after != True:
            logger.error("Variable value not updated correctly")
            test_passed = False
        
        # Test variable discovery
        var_count = client.get_variable_count()
        if var_count == 0:
            logger.error("No variables discovered")
            test_passed = False
        
        logger.info(f"Test completed. Variables discovered: {var_count}")
        
        # Disconnect
        await client.disconnect()
        
        return test_passed
        
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        return False


if __name__ == "__main__":
    # Run test when executed directly
    async def main():
        logging.basicConfig(level=logging.INFO)
        success = await test_opcua_server_with_mock_client()
        print(f"Test {'PASSED' if success else 'FAILED'}")
    
    asyncio.run(main())
