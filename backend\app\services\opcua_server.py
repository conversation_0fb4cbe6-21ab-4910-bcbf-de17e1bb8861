"""
OPC UA Server Manager
====================

Manages OPC UA server hosting simplified coordination variables for
backend-PLC communication in multi-material print job system.

Architecture Overview:
---------------------

┌─────────────────────────────────────────────────────────────────────────────┐
│                          Backend Application                                │
│                                                                             │
│  ┌─────────────────────┐                    ┌─────────────────────────────┐ │
│  │   FastAPI Server    │                    │   OPCUAServerManager        │ │
│  │   (Port 8000)       │                    │   (Port 4843)               │ │
│  │                     │                    │                             │ │
│  │ ┌─────────────────┐ │   write_variable() │ ┌─────────────────────────┐ │ │
│  │ │ REST Endpoints  │ │◄──────────────────►│ │  Coordination Variables │ │ │
│  │ │                 │ │   read_variable()  │ │                         │ │ │
│  │ │ /api/v1/print   │ │                    │ │ job_active: False       │ │ │
│  │ │ /api/v1/jobs    │ │                    │ │ recoater_ready_to_print │ │ │
│  │ │ /api/v1/...     │ │                    │ │ backend_error: False    │ │ │
│  │ └─────────────────┘ │                    │ │ plc_error: False        │ │ │
│  └─────────────────────┘                    │ └─────────────────────────┘ │ │
│                                             └─────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
                                                           │
                                                           │ OPC UA Protocol
                                                           │ opc.tcp://...
                                                           │
                                                   ┌───────▼──────┐
                                                   │ TwinCAT PLC  │
                                                   │ (OPC UA      │
                                                   │  Client)     │
                                                   └──────────────┘

Class Hierarchy:
---------------

┌────────────────────────────────────────────────────────────────────────────┐
│                           OPCUAServerManager                               │
├────────────────────────────────────────────────────────────────────────────┤
│ Attributes:                                                                │
│ ┌────────────────────────────────────────────────────────────────────────┐ │
│ │ config: OPCUAServerConfig        # Configuration from opcua_config.py  │ │
│ │ server: Optional[Server]         # asyncua Server instance             │ │
│ │ namespace_idx: int               # Registered namespace index (2)      │ │
│ │ variable_nodes: Dict[str, Any]   # name → OPC UA Node object mapping   │ │
│ │ _running: bool                   # Server state flag                   │ │
│ │ _restart_count: int              # Auto-restart attempt counter        │ │
│ │ _heartbeat_task: Optional[Task]  # Background heartbeat task           │ │
│ └────────────────────────────────────────────────────────────────────────┘ │
│                                                                            │
│ Public Methods:                                                            │
│ ┌────────────────────────────────────────────────────────────────────────┐ │
│ │ async start_server() → bool      # Initialize and start OPC UA server  │ │
│ │ async stop_server() → None       # Gracefully stop server              │ │
│ │ async write_variable(name, val)  # Update coordination variable        │ │
│ │ async read_variable(name) → Any  # Read coordination variable          │ │
│ │ is_running → bool                # Check server status                 │ │
│ │ get_variable_names() → List[str] # List all variable names             │ │
│ └────────────────────────────────────────────────────────────────────────┘ │
│                                                                            │
│ Private Methods:                                                           │
│ ┌────────────────────────────────────────────────────────────────────────┐ │
│ │ _create_coordination_variables() # Create variables from config        │ │
│ │ _get_ua_data_type(str) → ua.Type # Convert string to OPC UA type       │ │
│ │ _heartbeat_loop()                # Background heartbeat updater        │ │
│ │ _cleanup()                       # Clean up resources on shutdown      │ │
│ │ _handle_server_error(Exception)  # Error handling with auto-restart    │ │
│ └────────────────────────────────────────────────────────────────────────┘ │
└────────────────────────────────────────────────────────────────────────────┘

Data Flow:
----------

Configuration Phase:
┌─────────────────┐    loads     ┌──────────────────┐    creates   ┌─────────────────┐
│ opcua_config.py │ ───────────► │OPCUAServerManager│ ───────────► │ asyncua.Server  │
│                 │              │                  │              │                 │
│ COORDINATION_   │              │ self.config      │              │ self.server     │
│ VARIABLES       │              │ self.variable_   │              │                 │
│ [...]           │              │ nodes = {}       │              │ Endpoint:       │
└─────────────────┘              └──────────────────┘              │ opc.tcp://...   │
                                                                   └─────────────────┘
Runtime Phase:
┌─────────────────┐   write_var   ┌──────────────────┐   OPC UA     ┌─────────────────┐
│ FastAPI         │ ────────────► |  variable_nodes  │ ───────────► │ TwinCAT PLC     │
│ Endpoints       │               │ ["job_active"]   │              │                 │
│                 │   read_var    │ → <Node Object>  │   Protocol   │ Reads/Writes    │
│ /api/v1/print   │ ◄─────────────│                  │ ◄─────────── │ Variables       │
└─────────────────┘               └──────────────────┘              └─────────────────┘

Variable Creation Process:
-------------------------

Step 1: Load Configuration
┌─────────────────────────────────┐
│ for var_def in                  │
│   COORDINATION_VARIABLES:       │
│                                 │
│   CoordinationVariable(         │
│     name="job_active",          │
│     data_type="Boolean",        │
│     initial_value=False         │
│   )                             │
└─────────────────────────────────┘
                │
                ▼
Step 2: Create OPC UA Variables
┌─────────────────────────────────┐
│ var_node = await                │
│   coordination_folder.          │
│   add_variable(                 │
│     namespace_idx,              │
│     var_def.name,               │
│     var_def.initial_value,      │
│     ua_type                     │
│   )                             │
└─────────────────────────────────┘
                │
                ▼
Step 3: Store Runtime Reference
┌─────────────────────────────────┐
│ self.variable_nodes[            │
│   var_def.name                  │
│ ] = var_node                    │
│                                 │
│ # Now accessible via:           │
│ # write_variable("job_active")  │
└─────────────────────────────────┘

Server Lifecycle:
----------------

┌─────────────┐   start_server()  ┌─────────────┐   variables   ┌─────────────┐
│ Initialized │ ────────────────► │   Running   │ ────────────► │   Active    │
│             │                   │             │               │             │
│ _running =  │                   │ _running =  │               │ PLC can     │
│ False       │                   │ True        │               │ connect     │
└─────────────┘                   └─────────────┘               └─────────────┘
       ▲                                 │                             │
       │          _handle_server_error() │                             │
       │ ◄───────────────────────────────┘                             │
       │                                                               │ stop_server()
       │ ◄─────────────────────────────────────────────────────────────┘
       │
┌─────────────┐
│   Stopped   │
│             │
│ _running =  │
│ False       │
└─────────────┘

Usage Examples:
--------------

# Backend startup
opcua_manager = OPCUAServerManager()
await opcua_manager.start_server()

# Job coordination
await opcua_manager.write_variable("job_active", True)
await opcua_manager.write_variable("coordination_status", "uploading")

# Status monitoring
drum_ready = await opcua_manager.read_variable("drum0_ready")
current_layer = await opcua_manager.read_variable("current_layer")

# PLC interaction (from PLC side)
plc_client.connect("opc.tcp://*************:4843/recoater/server/")
job_status = plc_client.read("ns=2;s=job_active")  # Gets True
plc_client.write("ns=2;s=print_start_signal", True)
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from asyncua import Server, ua
from asyncua.common.subscription import DataChangeNotif

from app.config.opcua_config import opcua_config, COORDINATION_VARIABLES, CoordinationVariable

logger = logging.getLogger(__name__)


class OPCUAServerManager:
    """
    Manages OPC UA server for hosting simplified coordination variables.

    This server hosts 7 coordination variables that the TwinCAT PLC can read/write
    for backend-PLC coordination in multi-material print jobs.

    Hosted Variables:
    - job_active, total_layers, current_layer (Job Control)
    - recoater_ready_to_print, recoater_layer_complete (Recoater Coordination)
    - backend_error, plc_error (Error Handling)
    """
    
    def __init__(self, config=None):
        """
        Initialize OPC UA server manager.
        
        Args:
            config: Optional configuration override
        """
        # To toggle between global (opcua_config) and locally passed in (config)
        self.config = config or opcua_config
        # To store reference to self for instance functions
        self.server: Optional[Server] = None
        self.namespace_idx: int = 0
        self.variable_nodes: Dict[str, Any] = {}
        self._running = False
        self._restart_count = 0
        self._heartbeat_task: Optional[asyncio.Task] = None
        
        logger.info(f"OPC UA Server Manager initialized with endpoint: {self.config.endpoint}")
    
    async def start_server(self) -> bool:
        """
        Start the OPC UA server and initialize variables.
        
        Returns:
            bool: True if server started successfully
        """
        try:
            if self._running:
                logger.warning("OPC UA server is already running")
                return True
            
            # Initialize server
            self.server = Server()
            await self.server.init()
            
            # Configure server
            self.server.set_endpoint(self.config.endpoint)
            self.server.set_server_name(self.config.server_name)
            
            # Register namespace
            self.namespace_idx = await self.server.register_namespace(self.config.namespace_uri)
            logger.info(f"Registered namespace '{self.config.namespace_uri}' with index {self.namespace_idx}")
            
            # Create coordination variables
            await self._create_coordination_variables()
            
            # Start server
            async with self.server:
                self._running = True
                self._restart_count = 0
                logger.info(f"OPC UA server started successfully on {self.config.endpoint}")
                
                # Start heartbeat task
                self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
                
                # Keep server running
                try:
                    while self._running:
                        await asyncio.sleep(1)
                except asyncio.CancelledError:
                    logger.info("OPC UA server shutdown requested")
                finally:
                    await self._cleanup()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start OPC UA server: {e}")
            await self._handle_server_error(e)
            return False
    
    async def stop_server(self) -> None:
        """Stop the OPC UA server gracefully."""
        try:
            self._running = False
            
            # Cancel heartbeat task
            if self._heartbeat_task and not self._heartbeat_task.done():
                self._heartbeat_task.cancel()
                try:
                    await self._heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            # Stop server
            if self.server:
                logger.info("Stopping OPC UA server...")
                # Server will be stopped by exiting the async context manager
            
            logger.info("OPC UA server stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping OPC UA server: {e}")
    
    async def _create_coordination_variables(self) -> None:
        """Create all coordination variables in the server address space."""
        try:
            # The objects node is a standard entry point in the address space where all application-specific objects and folders are organized
            # "root" directory for custom data structures
            objects = self.server.get_objects_node()
            
            # Create coordination folder within this directory for custom nodes
            coordination_folder = await objects.add_folder(
                self.namespace_idx, 
                "MultiMaterialCoordination"
            )
            
            # Create variables
            for var_def in COORDINATION_VARIABLES:
                try:
                    # Determine OPC UA data type
                    ua_type = self._get_ua_data_type(var_def.data_type)
                    
                    # Create variable node
                    var_node = await coordination_folder.add_variable(
                        self.namespace_idx,
                        var_def.name,
                        var_def.initial_value,
                        ua_type
                    )
                    
                    # Set writable if specified
                    if var_def.writable:
                        await var_node.set_writable()
                    
                    # Set description
                    if var_def.description:
                        await var_node.set_description(var_def.description)
                    
                    # Store reference
                    self.variable_nodes[var_def.name] = var_node
                    
                    logger.debug(f"Created variable: {var_def.name} = {var_def.initial_value}")
                    
                except Exception as e:
                    logger.error(f"Failed to create variable {var_def.name}: {e}")
            
            logger.info(f"Created {len(self.variable_nodes)} coordination variables")
            
        except Exception as e:
            logger.error(f"Failed to create coordination variables: {e}")
            raise
    
    def _get_ua_data_type(self, data_type: str) -> ua.VariantType:
        """
        Convert string data type to OPC UA VariantType.
        
        Args:
            data_type: String representation of data type
            
        Returns:
            ua.VariantType: OPC UA variant type
        """
        type_mapping = {
            "Boolean": ua.VariantType.Boolean,
            "String": ua.VariantType.String,
            "Int32": ua.VariantType.Int32,
            "Float": ua.VariantType.Float,
            "Double": ua.VariantType.Double,
            "DateTime": ua.VariantType.DateTime
        }
        
        return type_mapping.get(data_type, ua.VariantType.String)
    
    async def write_variable(self, name: str, value: Any) -> bool:
        """
        Write value to a coordination variable.
        
        Args:
            name: Variable name
            value: Value to write
            
        Returns:
            bool: True if write successful
        """
        try:
            if not self._running:
                logger.warning(f"Cannot write variable {name}: server not running")
                return False
            
            if name not in self.variable_nodes:
                logger.error(f"Variable {name} not found")
                return False
            
            node = self.variable_nodes[name]
            await node.write_value(value)
            
            logger.debug(f"Updated variable {name} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to write variable {name}: {e}")
            return False
    
    async def read_variable(self, name: str) -> Any:
        """
        Read value from a coordination variable.
        
        Args:
            name: Variable name
            
        Returns:
            Any: Variable value or None if error
        """
        try:
            if not self._running:
                logger.warning(f"Cannot read variable {name}: server not running")
                return None
            
            if name not in self.variable_nodes:
                logger.error(f"Variable {name} not found")
                return None
            
            node = self.variable_nodes[name]
            value = await node.read_value()
            
            return value
            
        except Exception as e:
            logger.error(f"Failed to read variable {name}: {e}")
            return None
    
    async def _heartbeat_loop(self) -> None:
        """Update heartbeat variable periodically."""
        try:
            while self._running:
                await self.write_variable("backend_heartbeat", datetime.now(datetime.timezone.utc))
                await asyncio.sleep(5)  # Update every 5 seconds
        except asyncio.CancelledError:
            logger.debug("Heartbeat loop cancelled")
        except Exception as e:
            logger.error(f"Heartbeat loop error: {e}")
    
    async def _cleanup(self) -> None:
        """Clean up server resources."""
        try:
            self.variable_nodes.clear()
            self.server = None
            self._running = False
            logger.debug("OPC UA server cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    async def _handle_server_error(self, error: Exception) -> None:
        """
        Handle server errors with auto-restart if configured.
        
        Args:
            error: Exception that occurred
        """
        logger.error(f"OPC UA server error: {error}")
        
        if self.config.auto_restart and self._restart_count < self.config.max_restart_attempts:
            self._restart_count += 1
            logger.info(f"Attempting server restart ({self._restart_count}/{self.config.max_restart_attempts})")
            
            await asyncio.sleep(self.config.restart_delay)
            
            # Attempt restart
            try:
                await self.start_server()
            except Exception as restart_error:
                logger.error(f"Server restart failed: {restart_error}")
        else:
            logger.error("Max restart attempts reached or auto-restart disabled")
    
    @property
    def is_running(self) -> bool:
        """Check if server is currently running."""
        return self._running
    
    def get_variable_names(self) -> List[str]:
        """Get list of all variable names."""
        return list(self.variable_nodes.keys())


# Global server manager instance
opcua_server_manager = OPCUAServerManager()
