"""
Mock Recoater Client Service
===========================

This module provides a MockRecoaterClient class that simulates the behavior
of the real recoater hardware for development and testing purposes.

The mock client returns realistic sample data and simulates state changes
to allow frontend development without requiring actual hardware.
"""

import time
import random
import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


class MockRecoaterClient:
    """
    Mock client that simulates the Aerosint SPD Recoater hardware API.
    
    This class implements the same interface as RecoaterClient but returns
    mock data instead of making actual HTTP requests to hardware.
    """
    
    def __init__(self, base_url: str, timeout: float = 5.0):
        """
        Initialize the MockRecoaterClient.
        
        Args:
            base_url: The base URL (ignored in mock mode)
            timeout: Request timeout (ignored in mock mode)
        """
        self.base_url = base_url
        self.timeout = timeout
        self._start_time = time.time()
        self._state = {
            "status": "idle",
            "current_layer": 0,
            "total_layers": 100,
            "progress": 0.0,
            "temperature": 25.0,
            "errors": []
        }

        # Print job state management
        self._print_job_state = "ready"
        self._print_job_id = None
        self._print_job_start_time = None

        # Drum geometry storage for persistent mock data
        self._drum_geometries = {}  # {drum_id: {"file_data": bytes, "content_type": str}}

        # Drum state management for coordination engine
        self._drum_states = {}  # {drum_id: state_string}
        self._current_layers = {}  # {drum_id: current_layer_number}

        logger.info(f"MockRecoaterClient initialized (development mode)")
    
    def get_state(self) -> Dict[str, Any]:
        """
        Get the current state of the recoater.
        
        Returns:
            Dictionary containing current recoater state
        """
        # Simulate some dynamic changes
        elapsed = time.time() - self._start_time
        
        # Simulate temperature fluctuation
        self._state["temperature"] = 25.0 + random.uniform(-2.0, 2.0)
        
        # Simulate progress if running
        if self._state["status"] == "running":
            progress = min(0.95, (elapsed % 300) / 300)  # 5-minute cycle
            self._state["progress"] = progress
            self._state["current_layer"] = int(progress * self._state["total_layers"])
        
        logger.debug(f"Mock state: {self._state}")
        return self._state.copy()

    def get_config(self) -> Dict[str, Any]:
        """
        Mock implementation for getting recoater configuration.

        Returns:
            Dictionary containing mock configuration variables matching the hardware API schema
        """
        logger.info("Mock get_config called")
        return {
            "build_space_diameter": 250.0,
            "build_space_dimensions": {
                "length": 250.0,
                "width": 96.0
            },
            "ejection_matrix_size": 192,
            "gaps": [130.0, 130.0],  # Gaps between 3 drums
            "resolution": 500
        }

    def set_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Mock implementation for setting recoater configuration.

        Args:
            config: Configuration dictionary

        Returns:
            Mock response indicating success
        """
        logger.info(f"Mock set_config called with: {config}")
        return {
            "success": True,
            "config_updated": config,
            "timestamp": time.time()
        }

    def get_status(self) -> Dict[str, Any]:
        """
        Get the current status of the recoater.
        
        Returns:
            Dictionary containing status information
        """
        return {
            "status": self._state["status"],
            "uptime": int(time.time() - self._start_time),
            "version": "1.0.0-mock",
            "hardware_id": "MOCK-RECOATER-001"
        }
    
    def start_job(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Start a recoating job.
        
        Args:
            job_data: Job configuration data
            
        Returns:
            Dictionary containing job start response
        """
        if self._state["status"] != "idle":
            return {
                "success": False,
                "error": "Recoater is not idle"
            }
        
        self._state["status"] = "running"
        self._state["current_layer"] = 0
        self._state["progress"] = 0.0
        self._state["total_layers"] = job_data.get("layers", 100)
        
        logger.info(f"Mock job started with {self._state['total_layers']} layers")
        return {
            "success": True,
            "job_id": f"mock-job-{int(time.time())}"
        }
    
    def stop_job(self) -> Dict[str, Any]:
        """
        Stop the current recoating job.
        
        Returns:
            Dictionary containing job stop response
        """
        if self._state["status"] != "running":
            return {
                "success": False,
                "error": "No job is currently running"
            }
        
        self._state["status"] = "idle"
        self._state["current_layer"] = 0
        self._state["progress"] = 0.0
        
        logger.info("Mock job stopped")
        return {
            "success": True
        }
    
    def pause_job(self) -> Dict[str, Any]:
        """
        Pause the current recoating job.
        
        Returns:
            Dictionary containing job pause response
        """
        if self._state["status"] != "running":
            return {
                "success": False,
                "error": "No job is currently running"
            }
        
        self._state["status"] = "paused"
        
        logger.info("Mock job paused")
        return {
            "success": True
        }
    
    def resume_job(self) -> Dict[str, Any]:
        """
        Resume a paused recoating job.
        
        Returns:
            Dictionary containing job resume response
        """
        if self._state["status"] != "paused":
            return {
                "success": False,
                "error": "No job is currently paused"
            }
        
        self._state["status"] = "running"
        
        logger.info("Mock job resumed")
        return {
            "success": True
        }
    
    def get_axis_position(self, axis: str) -> Dict[str, Any]:
        """
        Get the current position of a specific axis.
        
        Args:
            axis: Axis name (x, y, z, etc.)
            
        Returns:
            Dictionary containing axis position
        """
        # Simulate some axis positions
        positions = {
            "x": 150.0 + random.uniform(-5.0, 5.0),
            "y": 100.0 + random.uniform(-3.0, 3.0),
            "z": 50.0 + random.uniform(-1.0, 1.0),
            "recoater": 0.0
        }
        
        position = positions.get(axis.lower(), 0.0)
        
        return {
            "axis": axis,
            "position": round(position, 2),
            "unit": "mm",
            "timestamp": time.time()
        }
    
    def move_axis(self, axis: str, position: float, speed: Optional[float] = None) -> Dict[str, Any]:
        """
        Move an axis to a specific position.
        
        Args:
            axis: Axis name
            position: Target position
            speed: Movement speed (optional)
            
        Returns:
            Dictionary containing move response
        """
        logger.info(f"Mock axis move: {axis} to {position}mm")
        
        return {
            "success": True,
            "axis": axis,
            "target_position": position,
            "estimated_time": abs(position) / 10.0  # Mock 10mm/s speed
        }
    
    def home_axis(self, axis: str) -> Dict[str, Any]:
        """
        Home a specific axis.

        Args:
            axis: Axis name

        Returns:
            Dictionary containing home response
        """
        logger.info(f"Mock axis home: {axis}")

        return {
            "success": True,
            "axis": axis,
            "estimated_time": 5.0  # Mock 5 second homing
        }

    def get_axis_status(self, axis: str) -> Dict[str, Any]:
        """
        Get the status of a specific axis.

        Args:
            axis: Axis name

        Returns:
            Dictionary containing axis status
        """
        # Get position data and add status info
        position_data = self.get_axis_position(axis)

        return {
            **position_data,
            "status": "idle",
            "homed": True,
            "enabled": True,
            "error": None
        }

    def get_gripper_state(self) -> Dict[str, Any]:
        """
        Get the current state of the gripper.

        Returns:
            Dictionary containing gripper state
        """
        return {
            "position": "open",  # or "closed"
            "pressure": 0.5,
            "status": "idle",
            "error": None,
            "timestamp": time.time()
        }

    def get_drums(self) -> list[dict[str, any]]:
        """
        Mock implementation for getting information about all drums.

        Returns:
            List of dictionaries containing drum information
        """
        logger.info("Mock get_drums called")
        return [
            {"id": 0, "name": "Drum 0 (Mock)"},
            {"id": 1, "name": "Drum 1 (Mock)"},
            {"id": 2, "name": "Drum 2 (Mock)"}
        ]

    def get_drum(self, drum_id: int) -> dict[str, any]:
        """
        Mock implementation for getting information about a specific drum.

        Args:
            drum_id: The drum's ID

        Returns:
            Dictionary containing drum information
        """
        logger.info(f"Mock get_drum for id {drum_id} called")

        # Ensure drum state is initialized
        if drum_id not in self._drum_states:
            self._drum_states[drum_id] = "ready"

        return {
            "id": drum_id,
            "state": self._drum_states.get(drum_id, "ready"),
            "circumference": 314.15,
            "position": round(random.uniform(0, 314), 2),
            "running": random.choice([True, False]),
            "current_layer": self._current_layers.get(drum_id, 0)
        }

    def set_state(self, action: str) -> Dict[str, Any]:
        """
        Mock implementation for setting recoater server state.

        Args:
            action: The action to perform ('restart' or 'shutdown')

        Returns:
            Mock response indicating command accepted
        """
        logger.info(f"Mock set_state called with action: {action}")
        return {
            "success": True,
            "action": action,
            "status": "accepted",
            "message": f"Mock server {action} command accepted"
        }

    def health_check(self) -> bool:
        """
        Mock implementation for health check. Always returns True.

        Returns:
            True indicating the mock recoater is always healthy
        """
        logger.info("Mock health_check called")
        return True

    # Drum Control Methods
    # Mock implementations for drum control

    def get_drum_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock implementation for getting drum motion.

        Args:
            drum_id: The drum's ID

        Returns:
            Mock motion information
        """
        logger.info(f"Mock drum motion for drum {drum_id}")

        return {
            "mode": random.choice(["absolute", "relative", "turns", "speed", "homing"]),
            "speed": random.uniform(10, 50),
            "distance": random.uniform(50, 200),
            "turns": random.uniform(1, 5)
        }

    def set_drum_motion(self, drum_id: int, mode: str, speed: float, distance: float = None, turns: float = None) -> Dict[str, Any]:
        """
        Mock implementation for setting drum motion.

        Args:
            drum_id: The drum's ID
            mode: Motion mode
            speed: Speed in mm/s
            distance: Distance in mm
            turns: Number of turns

        Returns:
            Mock motion response
        """
        logger.info(f"Mock drum motion for drum {drum_id}: mode={mode}, speed={speed}")

        return {
            "success": True,
            "drum_id": drum_id,
            "mode": mode,
            "speed": speed,
            "distance": distance,
            "turns": turns,
            "estimated_time": random.uniform(5.0, 30.0)
        }

    def cancel_drum_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock implementation for cancelling drum motion.

        Args:
            drum_id: The drum's ID

        Returns:
            Mock cancellation response
        """
        logger.info(f"Mock cancel drum motion for drum {drum_id}")

        return {
            "success": True,
            "drum_id": drum_id,
            "action": "motion_cancelled"
        }

    def get_drum_ejection(self, drum_id: int, unit: str = "pascal") -> Dict[str, Any]:
        """
        Mock implementation for getting drum ejection pressure.

        Args:
            drum_id: The drum's ID
            unit: Pressure unit

        Returns:
            Mock ejection pressure information
        """
        logger.info(f"Mock drum ejection for drum {drum_id}")

        return {
            "maximum": 500.0,
            "target": random.uniform(100, 300),
            "value": random.uniform(80, 320),
            "unit": unit
        }

    def set_drum_ejection(self, drum_id: int, target: float, unit: str = "pascal") -> Dict[str, Any]:
        """
        Mock implementation for setting drum ejection pressure.

        Args:
            drum_id: The drum's ID
            target: Target pressure
            unit: Pressure unit

        Returns:
            Mock ejection response
        """
        logger.info(f"Mock drum ejection for drum {drum_id}: target={target} {unit}")

        return {
            "success": True,
            "drum_id": drum_id,
            "target_pressure": target,
            "unit": unit
        }

    def get_drum_suction(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock implementation for getting drum suction pressure.

        Args:
            drum_id: The drum's ID

        Returns:
            Mock suction pressure information
        """
        logger.info(f"Mock drum suction for drum {drum_id}")

        return {
            "maximum": 5.0,
            "target": random.uniform(1.0, 3.0),
            "value": random.uniform(0.8, 3.2)
        }

    def set_drum_suction(self, drum_id: int, target: float) -> Dict[str, Any]:
        """
        Mock implementation for setting drum suction pressure.

        Args:
            drum_id: The drum's ID
            target: Target pressure

        Returns:
            Mock suction response
        """
        logger.info(f"Mock drum suction for drum {drum_id}: target={target}")

        return {
            "success": True,
            "drum_id": drum_id,
            "target_pressure": target,
            "unit": "Pa"
        }

    # Blade Control Methods
    # Mock implementations for blade/scraping control

    def get_blade_screws_info(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock implementation for getting blade screws info.

        Args:
            drum_id: The drum's ID

        Returns:
            Mock blade screws information
        """
        logger.info(f"Mock blade screws info for drum {drum_id}")

        return [
            {
                "id": 0,
                "position": random.uniform(0, 10000),  # µm
                "running": random.choice([True, False])
            },
            {
                "id": 1,
                "position": random.uniform(0, 10000),  # µm
                "running": random.choice([True, False])
            }
        ]

    def get_blade_screws_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock implementation for getting blade screws motion.

        Args:
            drum_id: The drum's ID

        Returns:
            Mock motion information or empty dict
        """
        logger.info(f"Mock blade screws motion for drum {drum_id}")

        # Randomly return motion or no motion
        if random.choice([True, False]):
            return {
                "mode": random.choice(["absolute", "relative", "homing"]),
                "distance": random.uniform(100, 5000)  # µm
            }
        else:
            return {}  # No current motion

    def set_blade_screws_motion(self, drum_id: int, mode: str, distance: float = None) -> Dict[str, Any]:
        """
        Mock implementation for setting blade screws motion.

        Args:
            drum_id: The drum's ID
            mode: Motion mode
            distance: Distance in µm

        Returns:
            Mock motion response
        """
        logger.info(f"Mock blade screws motion for drum {drum_id}: mode={mode}, distance={distance}")

        return {
            "success": True,
            "drum_id": drum_id,
            "mode": mode,
            "distance": distance,
            "estimated_time": random.uniform(1.0, 10.0)
        }

    def cancel_blade_screws_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock implementation for cancelling blade screws motion.

        Args:
            drum_id: The drum's ID

        Returns:
            Mock cancellation response
        """
        logger.info(f"Mock cancel blade screws motion for drum {drum_id}")

        return {
            "success": True,
            "drum_id": drum_id,
            "action": "motion_cancelled"
        }

    def get_blade_screw_info(self, drum_id: int, screw_id: int) -> Dict[str, Any]:
        """
        Mock implementation for getting individual blade screw info.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID

        Returns:
            Mock blade screw information
        """
        logger.info(f"Mock blade screw info for drum {drum_id}, screw {screw_id}")

        return {
            "id": screw_id,
            "position": random.uniform(0, 10000),  # µm
            "running": random.choice([True, False])
        }

    def get_blade_screw_motion(self, drum_id: int, screw_id: int) -> Dict[str, Any]:
        """
        Mock implementation for getting individual blade screw motion.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID

        Returns:
            Mock motion information or empty dict
        """
        logger.info(f"Mock blade screw motion for drum {drum_id}, screw {screw_id}")

        # Randomly return motion or no motion
        if random.choice([True, False]):
            return {
                "distance": random.uniform(100, 5000)  # µm
            }
        else:
            return {}  # No current motion

    def set_blade_screw_motion(self, drum_id: int, screw_id: int, distance: float) -> Dict[str, Any]:
        """
        Mock implementation for setting individual blade screw motion.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID
            distance: Distance in µm

        Returns:
            Mock motion response
        """
        logger.info(f"Mock blade screw motion for drum {drum_id}, screw {screw_id}: distance={distance}")

        return {
            "success": True,
            "drum_id": drum_id,
            "screw_id": screw_id,
            "distance": distance,
            "estimated_time": random.uniform(1.0, 5.0)
        }

    # Leveler Control Mock Methods

    def get_leveler_pressure(self) -> Dict[str, Any]:
        """
        Mock implementation for getting leveler pressure.

        Returns:
            Mock leveler pressure information
        """
        logger.info("Mock get leveler pressure")

        return {
            "maximum": 10.0,  # Maximum pressure in Pa
            "target": random.uniform(2.0, 8.0),  # Target pressure in Pa
            "value": random.uniform(1.8, 8.2)  # Current pressure in Pa
        }

    def set_leveler_pressure(self, target: float) -> Dict[str, Any]:
        """
        Mock implementation for setting leveler pressure.

        Args:
            target: Target pressure in Pa

        Returns:
            Mock pressure response
        """
        logger.info(f"Mock set leveler pressure: target={target}")

        return {
            "success": True,
            "target_pressure": target,
            "unit": "Pa"
        }

    def get_leveler_sensor(self) -> Dict[str, Any]:
        """
        Mock implementation for getting leveler sensor state.

        Returns:
            Mock sensor state
        """
        logger.info("Mock get leveler sensor state")

        return {
            "state": random.choice([True, False])  # Randomly return true or false
        }

    def cancel_blade_screw_motion(self, drum_id: int, screw_id: int) -> Dict[str, Any]:
        """
        Mock implementation for cancelling individual blade screw motion.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID

        Returns:
            Mock cancellation response
        """
        logger.info(f"Mock cancel blade screw motion for drum {drum_id}, screw {screw_id}")

        return {
            "success": True,
            "drum_id": drum_id,
            "screw_id": screw_id,
            "action": "motion_cancelled"
        }

    # Print Control Methods
    # Mock implementations for print control endpoints

    def get_layer_parameters(self) -> Dict[str, Any]:
        """
        Mock implementation for getting layer parameters.

        Returns:
            Mock layer parameters
        """
        logger.info("Mock get layer parameters")

        return {
            "filling_id": 1,
            "speed": 30.0,
            "powder_saving": True,
            "x_offset": 0.0,
            "max_x_offset": 100.0
        }

    def set_layer_parameters(self, filling_id: int, speed: float, powder_saving: bool = True, x_offset: float = None) -> Dict[str, Any]:
        """
        Mock implementation for setting layer parameters.

        Args:
            filling_id: The ID of the drum with the filling material powder
            speed: The patterning speed [mm/s]
            powder_saving: Flag indicating if powder saving strategies are used
            x_offset: The offset along the X axis [mm]

        Returns:
            Mock success response
        """
        logger.info(f"Mock set layer parameters: filling_id={filling_id}, speed={speed}, powder_saving={powder_saving}, x_offset={x_offset}")

        return {
            "success": True,
            "filling_id": filling_id,
            "speed": speed,
            "powder_saving": powder_saving,
            "x_offset": x_offset
        }

    def get_layer_preview(self) -> bytes:
        """
        Mock implementation for getting layer preview.
        Returns a color-coded preview showing the current layer configuration
        with proper drum colors matching the Legend component.

        Returns:
            Mock PNG image data as bytes with color-coded geometry
        """
        logger.info("Mock get layer preview - generating color-coded preview")

        try:
            # Import here to avoid circular imports
            from services.cli_parser import CliParserService

            # Create CLI parser and generate color-coded preview
            parser = CliParserService(logger=logger)
            png_bytes = parser.render_layer_configuration_preview(width=800, height=600)

            logger.info("Generated color-coded layer configuration preview")
            return png_bytes

        except Exception as e:
            logger.error(f"Failed to generate color-coded preview: {e}")
            # Fallback to simple PNG if generation fails
            mock_png = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
            return mock_png

    def start_print_job(self) -> Dict[str, Any]:
        """
        Mock implementation for starting a print job.

        Returns:
            Mock success response
        """
        logger.info("Mock start print job")

        # Update print job state
        self._print_job_state = "printing"
        self._print_job_id = f"job_{int(time.time())}"
        self._print_job_start_time = time.time()

        return {
            "success": True,
            "job_id": self._print_job_id,
            "status": "started"
        }

    def cancel_print_job(self) -> Dict[str, Any]:
        """
        Mock implementation for cancelling a print job.

        Returns:
            Mock success response
        """
        logger.info("Mock cancel print job")

        # Update print job state
        self._print_job_state = "ready"
        self._print_job_id = None
        self._print_job_start_time = None

        return {
            "success": True,
            "status": "cancelled"
        }

    # File Management Methods
    # Mock implementations for drum geometry file management

    def upload_drum_geometry(self, drum_id: int, file_data: bytes, content_type: str = "application/octet-stream") -> Dict[str, Any]:
        """
        Mock implementation for uploading drum geometry file.

        Args:
            drum_id: The ID of the drum to upload geometry to
            file_data: The binary file data to upload
            content_type: The content type of the file

        Returns:
            Mock success response
        """
        logger.info(f"Mock upload drum geometry: drum_id={drum_id}, file_size={len(file_data)}, content_type={content_type}")

        # Store the uploaded file data for persistent mock storage
        self._drum_geometries[drum_id] = {
            "file_data": file_data,
            "content_type": content_type
        }

        # Update drum state to simulate processing
        if drum_id not in self._drum_states:
            self._drum_states[drum_id] = "ready"

        # Simulate upload processing time
        self._drum_states[drum_id] = "uploading"

        # Schedule state change back to ready after a delay
        import threading
        def reset_to_ready():
            time.sleep(2)  # 2 second processing time
            self._drum_states[drum_id] = "ready"

        threading.Thread(target=reset_to_ready, daemon=True).start()
        logger.info(f"Stored geometry file for drum {drum_id} in mock storage")

        return {
            "success": True,
            "drum_id": drum_id,
            "file_size": len(file_data),
            "content_type": content_type,
            "message": "Geometry file uploaded successfully"
        }

    # Multi-Material Coordination Methods (Stage 3)
    # These methods support the coordination engine for multi-drum operations

    async def upload_cli_data(self, drum_id: int, cli_data: bytes) -> Dict[str, Any]:
        """
        Mock async implementation for uploading CLI data to a specific drum.

        Args:
            drum_id: Target drum (0, 1, or 2)
            cli_data: ASCII CLI data to upload

        Returns:
            Mock response from the API
        """
        import asyncio

        # Simulate async upload with small delay
        await asyncio.sleep(0.1)

        # Use the existing upload_drum_geometry method
        return self.upload_drum_geometry(drum_id, cli_data, "application/octet-stream")

    async def get_drum_status(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock async implementation for getting drum status.

        Args:
            drum_id: Target drum (0, 1, or 2)

        Returns:
            Dictionary containing drum status with 'ready' field
        """
        import asyncio

        # Simulate async status check with small delay
        await asyncio.sleep(0.05)

        # Get current drum state - ensure drums are initialized as ready
        if drum_id not in self._drum_states:
            self._drum_states[drum_id] = "ready"

        state = self._drum_states.get(drum_id, "ready")
        ready = state in ["ready", "idle", "standby"]

        return {
            "drum_id": drum_id,
            "state": state,
            "ready": ready,
            "current_layer": self._current_layers.get(drum_id, 0),
            "raw_info": self.get_drum(drum_id)
        }

    async def get_multimaterial_status(self) -> Dict[str, Any]:
        """
        Mock async implementation for getting status of all drums.

        Returns:
            Dictionary containing status for all 3 drums
        """
        import asyncio

        # Get status for all drums
        drum_statuses = {}
        all_ready = True

        for drum_id in [0, 1, 2]:
            status = await self.get_drum_status(drum_id)
            drum_statuses[str(drum_id)] = status
            if not status.get("ready", False):
                all_ready = False

        return {
            "timestamp": time.time(),
            "job_active": self._job_active,
            "drums": drum_statuses,
            "all_ready": all_ready,
            "errors": []
        }

    async def monitor_drum_state_transitions(self, drum_id: int, expected_states: List[str],
                                           timeout: float = 30.0, poll_interval: float = 1.0) -> bool:
        """
        Mock async implementation for monitoring drum state transitions.

        Args:
            drum_id: Target drum (0, 1, or 2)
            expected_states: List of states to wait for (in order)
            timeout: Maximum time to wait (seconds)
            poll_interval: Time between status checks (seconds)

        Returns:
            bool: True if all expected states were observed
        """
        import asyncio

        logger.info(f"Mock monitoring drum {drum_id} for state transitions: {expected_states}")

        # For mock implementation, simulate successful state transitions
        for state in expected_states:
            await asyncio.sleep(poll_interval)
            self._drum_states[drum_id] = state
            logger.info(f"Mock drum {drum_id} transitioned to state: {state}")

        return True

    def set_multimaterial_job_active(self, active: bool) -> None:
        """
        Set the multi-material job active state for mock testing.

        Args:
            active: Whether the job is active
        """
        self._job_active = active
        logger.info(f"Mock multi-material job active: {active}")

    def advance_layer(self, drum_id: int) -> None:
        """
        Advance the current layer for a specific drum (for mock testing).

        Args:
            drum_id: Target drum (0, 1, or 2)
        """
        if drum_id in self._current_layers:
            self._current_layers[drum_id] += 1
        else:
            self._current_layers[drum_id] = 1

        logger.info(f"Mock drum {drum_id} advanced to layer {self._current_layers[drum_id]}")

    def reset_multimaterial_state(self) -> None:
        """Reset all multi-material state for mock testing."""
        self._drum_states = {0: "ready", 1: "ready", 2: "ready"}
        self._current_layers = {0: 0, 1: 0, 2: 0}
        self._job_active = False
        logger.info("Mock multi-material state reset")

    def download_drum_geometry(self, drum_id: int) -> bytes:
        """
        Mock implementation for downloading drum geometry file.

        Args:
            drum_id: The ID of the drum to download geometry from

        Returns:
            Stored file data if available, otherwise mock PNG image data as bytes
        """
        logger.info(f"Mock download drum geometry: drum_id={drum_id}")

        # Return stored file data if available
        if drum_id in self._drum_geometries:
            stored_data = self._drum_geometries[drum_id]["file_data"]
            logger.info(f"Returning stored geometry file for drum {drum_id} (size: {len(stored_data)} bytes)")
            return stored_data
        else:
            # Generate color-coded preview for the specific drum
            logger.info(f"No stored file for drum {drum_id}, generating color-coded drum preview")
            try:
                # Generate a simple, deterministic PNG for testing consistency
                # This avoids issues with random elements in PNG generation
                import io
                from PIL import Image, ImageDraw

                # Create a simple colored rectangle based on drum_id for consistency
                img = Image.new('RGB', (100, 100), color='white')
                draw = ImageDraw.Draw(img)

                # Use drum-specific colors for consistency
                colors = ['blue', 'orange', 'green']
                color = colors[drum_id % len(colors)]
                draw.rectangle([10, 10, 90, 90], fill=color)

                # Convert to bytes
                img_bytes = io.BytesIO()
                img.save(img_bytes, format='PNG')
                png_bytes = img_bytes.getvalue()

                logger.info(f"Generated color-coded preview for drum {drum_id}")
                return png_bytes

            except Exception as e:
                logger.error(f"Failed to generate color-coded drum preview: {e}")
                # Fallback to simple PNG if generation fails
                mock_png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\rIDATx\x9cc\xf8\x0f\x00\x00\x01\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00IEND\xaeB`\x82'
                return mock_png_data

    def delete_drum_geometry(self, drum_id: int) -> Dict[str, Any]:
        """
        Mock implementation for deleting drum geometry file.

        Args:
            drum_id: The ID of the drum to delete geometry from

        Returns:
            Mock success response
        """
        logger.info(f"Mock delete drum geometry: drum_id={drum_id}")

        # Remove stored file data if it exists
        if drum_id in self._drum_geometries:
            del self._drum_geometries[drum_id]
            logger.info(f"Removed stored geometry file for drum {drum_id} from mock storage")
        else:
            logger.info(f"No stored file found for drum {drum_id}, nothing to delete")

        return {
            "success": True,
            "drum_id": drum_id,
            "message": "Geometry file deleted successfully"
        }

    def get_print_job_status(self) -> Dict[str, Any]:
        """
        Mock implementation for getting print job status.

        Returns:
            Mock print job status
        """
        logger.info("Mock get print job status")

        # Use stable state instead of random
        current_state = self._print_job_state

        # Optionally simulate job completion after some time
        if (current_state == "printing" and
            self._print_job_start_time and
            time.time() - self._print_job_start_time > 30):  # Auto-complete after 30 seconds
            current_state = "ready"
            self._print_job_state = "ready"
            self._print_job_id = None
            self._print_job_start_time = None

        return {
            "state": current_state,
            "is_printing": current_state == "printing",
            "has_error": current_state == "error"
        }
