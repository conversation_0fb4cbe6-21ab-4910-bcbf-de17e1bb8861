"""
OPC UA Coordinator
==================

Provides a high-level interface for simplified OPC UA communication and coordination
with the TwinCAT PLC for backend-PLC coordination in multi-material print jobs.

Architecture Overview:
---------------------

┌──────────────────────────────────────────────────────────────────────────────┐
│                         Backend Application                                  │
│                                                                              │
│  ┌─────────────────────┐           ┌──────────────────────────────────────┐  │
│  │   FastAPI Endpoints │           │        OPCUACoordinator              │  │
│  │                     │           │      (High-Level Interface)          │  │
│  │ POST /api/jobs/start│──────────►│                                      │  │
│  │ GET  /api/jobs/     │           │  ┌─────────────────────────────────┐ │  │
│  │ POST /api/print/    │           │  │    Convenience Methods          │ │  │
│  │                     │           │  │                                 │ │  │
│  └─────────────────────┘           │  │ • set_job_active()              │ │  │
│                                    │  │ • set_recoater_ready_to_print() │ │  │
│                                    │  │ • update_layer_progress()       │ │  │
│                                    │  │ • set_backend_error()           │ │  │
│                                    │  └─────────────────────────────────┘ │  │
│                                    │           │                          │  │
│                                    │           ▼                          │  │
│                                    │  ┌─────────────────────────────────┐ │  │
│                                    │  │   OPCUAServerManager            │ │  │
│                                    │  │   (Low-Level Server)            │ │  │
│                                    │  │                                 │ │  │
│                                    │  │ • write_variable()              │ │  │
│                                    │  │ • read_variable()               │ │  │
│                                    │  │ • start_server()                │ │  │
│                                    │  └─────────────────────────────────┘ │  │
│                                    └──────────────────────────────────────┘  │
└──────────────────────────────────────────────────────────────────────────────┘
                                                    │
                                                    │ OPC UA Protocol
                                                    │ opc.tcp://...
                                                    │
                                            ┌───────▼──────┐
                                            │ TwinCAT PLC  │
                                            │ (OPC UA      │
                                            │  Client)     │
                                            └──────────────┘

Class Structure:
---------------

┌─────────────────────────────────────────────────────────────────────────────┐
│                           OPCUACoordinator                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ Attributes:                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ config: OPCUAServerConfig         # Configuration from opcua_config.py  │ │
│ │ server_manager: OPCUAServerManager # Reference to low-level server      │ │
│ │ _connected: bool                  # Connection state flag               │ │
│ │ _monitoring_task: Optional[Task]  # Background monitoring task          │ │
│ │ _event_handlers: Dict[str, List]  # Variable change event handlers      │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ Connection Management:                                                      │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ async connect() → bool            # Start coordinator and server        │ │
│ │ async disconnect() → bool         # Stop coordinator and server         │ │
│ │ is_connected() → bool             # Check connection status             │ │
│ │ get_server_status() → Dict        # Get comprehensive status info       │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ Variable Access (Low-Level):                                                │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ async write_variable(name, val)   # Write to any coordination variable  │ │
│ │ async read_variable(name) → Any   # Read from any coordination variable │ │
│ │ async subscribe_to_changes(...)   # Subscribe to variable change events │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ Job Management (High-Level):                                                │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ async set_job_active(id, layers)  # Activate job with coordination vars │ │
│ │ async set_job_inactive()          # Deactivate job and reset variables  │ │
│ │ async update_layer_progress(num)  # Update current layer number         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ Drum Coordination (High-Level):                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ async set_drums_ready(states)     # Set multiple drum ready states      │ │
│ │ async _reset_drum_status()        # Reset all drum variables to False   │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ Error Handling (High-Level):                                                │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ async set_error_state(...)        # Set error flags and message         │ │
│ │ async clear_error_flags()         # Clear all error variables           │ │
│ │ async _reset_process_signals()    # Reset process coordination flags    │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ Event System (High-Level):                                                  │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ async _monitoring_loop()          # Background task for change detection│ │
│ │ async _trigger_event_handlers()   # Execute registered callbacks        │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘

Abstraction Layers:
------------------

┌─────────────────────────────────────────────────────────────────────────────┐
│                       Application Layer                                     │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                        FastAPI Endpoints                                │ │
│ │ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────────────────┐ │ │
│ │ │ /api/jobs/start │ │ /api/print/     │ │ /api/status/                │ │ │
│ │ └─────────────────┘ └─────────────────┘ └─────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                   │                                         │
│                                   ▼                                         │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                    Business Logic Layer                                 │ │
│ │ ┌─────────────────────────────────────────────────────────────────────┐ │ │
│ │ │                    OPCUACoordinator                                 │ │ │
│ │ │                                                                     │ │ │
│ │ │ Business Methods:                   Helper Methods:                 │ │ │
│ │ │ • set_job_active()                  • _reset_drum_status()          │ │ │
│ │ │ • set_drums_ready()                 • _reset_process_signals()      │ │ │
│ │ │ • update_layer_progress()           • _monitoring_loop()            │ │ │
│ │ │ • set_error_state()                 • _trigger_event_handlers()     │ │ │
│ │ └─────────────────────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                   │                                         │
│                                   ▼                                         │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                     Protocol Layer                                      │ │
│ │ ┌─────────────────────────────────────────────────────────────────────┐ │ │
│ │ │                   OPCUAServerManager                                │ │ │
│ │ │                                                                     │ │ │
│ │ │ Protocol Methods:               Server Management:                  │ │ │
│ │ │ • write_variable()              • start_server()                    │ │ │
│ │ │ • read_variable()               • stop_server()                     │ │ │
│ │ │ • get_variable_names()          • _handle_server_error()            │ │ │
│ │ └─────────────────────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
                                   │
                                   ▼
                           ┌────────────────┐
                           │ asyncua.Server │
                           │ (OPC UA        │
                           │  Protocol)     │
                           └────────────────┘

Data Flow Patterns:
------------------

Job Activation Flow:
┌─────────────────┐   set_job_active()  ┌─────────────────┐   write_variable()  ┌─────────────────┐
│ FastAPI         │ ──────────────────► │ OPCUACoordinator│ ──────────────────► │ OPCUAServerMgr  │
│ /api/jobs/start │                     │                 │                     │                 │
└─────────────────┘                     │ Business Logic: │                     │ Protocol Logic: │
                                        │ • job_active=T  │                     │ • job_active    │
                                        │ • job_id=value  │                     │ • job_id        │
                                        │ • total_layers  │                     │ • total_layers  │
                                        │ • current=0     │                     │ • current_layer │
                                        │ • status=active │                     │ • coord_status  │
                                        └─────────────────┘                     └─────────────────┘

Drum Status Flow:
┌─────────────────┐   set_drums_ready() ┌─────────────────┐   Multiple writes   ┌─────────────────┐
│ Print Manager   │ ──────────────────► │ OPCUACoordinator│ ──────────────────► │ OPCUAServerMgr  │
│                 │ {0:True, 1:False,   │                 │                     │                 │
│                 │  2:True}            │ Business Logic: │                     │ Protocol Logic: │
└─────────────────┘                     │ • drum0_ready=T │                     │ • drum0_ready   │
                                        │ • drum1_ready=F │                     │ • drum1_ready   │
                                        │ • drum2_ready=T │                     │ • drum2_ready   │
                                        │ • all_ready=F   │                     │ • all_drums_rdy │
                                        └─────────────────┘                     └─────────────────┘

Event Handling Flow:
┌─────────────────┐    subscribe_to_    ┌─────────────────┐    _monitoring_     ┌─────────────────┐
│ Application     │    changes()        │ OPCUACoordinator│    loop()           │ Event Handlers  │
│ Components      │ ──────────────────► │                 │ ──────────────────► │                 │
│                 │                     │ • Store handlers│                     │ • on_drum_ready │
│                 │                     │ • Start monitor │                     │ • on_error      │
│                 │                     │ • Detect changes│                     │ • on_job_done   │
└─────────────────┘                     └─────────────────┘                     └─────────────────┘

Usage Examples:
--------------

# High-level job management
coordinator = OPCUACoordinator()
await coordinator.connect()

# Start a multi-material job
await coordinator.set_job_active("JOB_001", 150)

# Update drum status
await coordinator.set_drums_ready({0: True, 1: True, 2: False})

# Handle errors
await coordinator.set_error_state(backend_error=True, message="Drum 2 timeout")

# Low-level variable access
await coordinator.write_variable("custom_variable", "custom_value")
current_layer = await coordinator.read_variable("current_layer")
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime

from app.config.opcua_config import opcua_config, get_variable_by_name
from app.services.opcua_server import opcua_server_manager

logger = logging.getLogger(__name__)


class OPCUACoordinator:
    """
    High-level coordinator for simplified OPC UA communication.

    This class provides a simplified interface for managing the 7 coordination
    variables and communicating with the PLC for multi-material print jobs.

    Managed Variables:
    - job_active, total_layers, current_layer (Job Control)
    - recoater_ready_to_print, recoater_layer_complete (Recoater Coordination)
    - backend_error, plc_error (Error Handling)
    """
    
    def __init__(self, config=None):
        """
        Initialize OPC UA coordinator.
        
        Args:
            config: Optional configuration override
        """
        self.config = config or opcua_config
        self.server_manager = opcua_server_manager
        self._connected = False
        self._monitoring_task: Optional[asyncio.Task] = None
        self._event_handlers: Dict[str, List[Callable]] = {}
        
        logger.info("OPC UA Coordinator initialized")
    
    async def connect(self) -> bool:
        """
        Connect to OPC UA server and start coordination.
        
        Returns:
            bool: True if connection successful
        """
        try:
            if self._connected:
                logger.warning("OPC UA coordinator already connected")
                return True
            
            # Start server manager if not running
            if not self.server_manager.is_running:
                logger.info("Starting OPC UA server...")
                # Start server in background task
                asyncio.create_task(self.server_manager.start_server())
                
                # Wait for server to be ready
                for _ in range(10):  # Wait up to 10 seconds
                    await asyncio.sleep(1)
                    if self.server_manager.is_running:
                        break
                else:
                    logger.error("OPC UA server failed to start within timeout")
                    return False
            
            self._connected = True
            
            # Start monitoring task
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            logger.info("OPC UA coordinator connected successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect OPC UA coordinator: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """
        Disconnect from OPC UA server.
        
        Returns:
            bool: True if disconnection successful
        """
        try:
            self._connected = False
            
            # Cancel monitoring task
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
                try:
                    await self._monitoring_task
                except asyncio.CancelledError:
                    pass
            
            # Stop server manager
            await self.server_manager.stop_server()
            
            logger.info("OPC UA coordinator disconnected")
            return True
            
        except Exception as e:
            logger.error(f"Error disconnecting OPC UA coordinator: {e}")
            return False
    
    async def write_variable(self, name: str, value: Any) -> bool:
        """
        Write value to a coordination variable.
        
        Args:
            name: Variable name
            value: Value to write
            
        Returns:
            bool: True if write successful
        """
        if not self._connected:
            logger.warning(f"Cannot write variable {name}: not connected")
            return False
        
        success = await self.server_manager.write_variable(name, value)
        
        if success:
            # Trigger event handlers
            await self._trigger_event_handlers(name, value)
        
        return success
    
    async def read_variable(self, name: str) -> Any:
        """
        Read value from a coordination variable.
        
        Args:
            name: Variable name
            
        Returns:
            Any: Variable value or None if error
        """
        if not self._connected:
            logger.warning(f"Cannot read variable {name}: not connected")
            return None
        
        return await self.server_manager.read_variable(name)
    
    async def subscribe_to_changes(self, variables: List[str], handler: Callable) -> bool:
        """
        Subscribe to variable changes with a callback handler.
        
        Args:
            variables: List of variable names to monitor
            handler: Callback function to call on changes
            
        Returns:
            bool: True if subscription successful
        """
        try:
            for var_name in variables:
                if var_name not in self._event_handlers:
                    self._event_handlers[var_name] = []
                self._event_handlers[var_name].append(handler)
            
            logger.info(f"Subscribed to changes for variables: {variables}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to subscribe to variable changes: {e}")
            return False
    
    async def set_job_active(self, total_layers: int) -> bool:
        """
        Set job as active with coordination variables.

        Args:
            total_layers: Total number of layers

        Returns:
            bool: True if successful
        """
        try:
            success = True
            success &= await self.write_variable("job_active", True)
            success &= await self.write_variable("total_layers", total_layers)
            success &= await self.write_variable("current_layer", 0)

            if success:
                logger.info(f"Job set as active with {total_layers} layers")

            return success

        except Exception as e:
            logger.error(f"Failed to set job active: {e}")
            return False
    
    async def set_job_inactive(self) -> bool:
        """
        Set job as inactive and reset coordination variables.

        Returns:
            bool: True if successful
        """
        try:
            success = True
            success &= await self.write_variable("job_active", False)
            success &= await self.write_variable("total_layers", 0)
            success &= await self.write_variable("current_layer", 0)
            success &= await self.write_variable("recoater_ready_to_print", False)
            success &= await self.write_variable("recoater_layer_complete", False)

            if success:
                logger.info("Job set as inactive, coordination variables reset")

            return success

        except Exception as e:
            logger.error(f"Failed to set job inactive: {e}")
            return False
    
    async def update_layer_progress(self, current_layer: int) -> bool:
        """
        Update current layer progress.
        
        Args:
            current_layer: Current layer number
            
        Returns:
            bool: True if successful
        """
        return await self.write_variable("current_layer", current_layer)
    
    async def set_recoater_ready_to_print(self, ready: bool) -> bool:
        """
        Set recoater ready to print status.

        Args:
            ready: True when Aerosint is ready for print execution

        Returns:
            bool: True if successful
        """
        try:
            success = await self.write_variable("recoater_ready_to_print", ready)
            if success:
                logger.info(f"Recoater ready to print set to: {ready}")
            return success

        except Exception as e:
            logger.error(f"Failed to set recoater ready status: {e}")
            return False

    async def set_recoater_layer_complete(self, complete: bool) -> bool:
        """
        Set recoater layer complete status.

        Args:
            complete: True when layer deposition is complete

        Returns:
            bool: True if successful
        """
        try:
            success = await self.write_variable("recoater_layer_complete", complete)
            if success:
                logger.info(f"Recoater layer complete set to: {complete}")
            return success

        except Exception as e:
            logger.error(f"Failed to set layer complete status: {e}")
            return False
    
    async def set_backend_error(self, error: bool) -> bool:
        """
        Set backend error flag.

        Args:
            error: Backend error flag

        Returns:
            bool: True if successful
        """
        try:
            success = await self.write_variable("backend_error", error)
            if success and error:
                logger.warning("Backend error flag set")
            elif success and not error:
                logger.info("Backend error flag cleared")
            return success

        except Exception as e:
            logger.error(f"Failed to set backend error: {e}")
            return False

    async def set_plc_error(self, error: bool) -> bool:
        """
        Set PLC error flag.

        Args:
            error: PLC error flag

        Returns:
            bool: True if successful
        """
        try:
            success = await self.write_variable("plc_error", error)
            if success and error:
                logger.warning("PLC error flag set")
            elif success and not error:
                logger.info("PLC error flag cleared")
            return success

        except Exception as e:
            logger.error(f"Failed to set PLC error: {e}")
            return False
    
    async def clear_error_flags(self) -> bool:
        """
        Clear all error flags.

        Returns:
            bool: True if successful
        """
        try:
            success = True
            success &= await self.set_backend_error(False)
            success &= await self.set_plc_error(False)

            if success:
                logger.info("All error flags cleared")

            return success

        except Exception as e:
            logger.error(f"Failed to clear error flags: {e}")
            return False
    

    
    async def _monitoring_loop(self) -> None:
        """Monitor coordination variables for changes."""
        try:
            while self._connected:
                # This is a simplified monitoring loop
                # In a full implementation, this would use OPC UA subscriptions
                await asyncio.sleep(1)
                
        except asyncio.CancelledError:
            logger.debug("Monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Monitoring loop error: {e}")
    
    async def _trigger_event_handlers(self, variable_name: str, value: Any) -> None:
        """Trigger event handlers for variable changes."""
        try:
            if variable_name in self._event_handlers:
                for handler in self._event_handlers[variable_name]:
                    try:
                        if asyncio.iscoroutinefunction(handler):
                            await handler(variable_name, value)
                        else:
                            handler(variable_name, value)
                    except Exception as e:
                        logger.error(f"Error in event handler for {variable_name}: {e}")
        except Exception as e:
            logger.error(f"Error triggering event handlers: {e}")
    
    def is_connected(self) -> bool:
        """Check if coordinator is connected."""
        return self._connected
    
    def get_server_status(self) -> Dict[str, Any]:
        """Get server status information."""
        return {
            "connected": self._connected,
            "server_running": self.server_manager.is_running,
            "endpoint": self.config.endpoint,
            "namespace": self.config.namespace_uri,
            "variable_count": len(self.server_manager.get_variable_names())
        }


# Global coordinator instance
opcua_coordinator = OPCUACoordinator()
