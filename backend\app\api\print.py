"""
Print API Router
===============

This module provides FastAPI endpoints for print control operations including
layer parameters management and preview functionality.

The endpoints follow the openapi.json specification for print-related operations.
"""

from fastapi import APIRouter, HTTPException, Depends, Response, UploadFile, File, Path, Body
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, Optional, Tuple
import logging
import uuid
import os
import tempfile
from pathlib import Path as PathLib

from app.dependencies import get_recoater_client, get_multilayer_job_manager
from services.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError
from services.cli_parser import CliParserService, CliParsingError, ParsedCliFile
from app.services.multilayer_job_manager import MultiMaterialJobManager, MultiMaterialJobError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/print", tags=["print"])

# In-memory cache for storing parsed CLI files
# Key: file_id (UUID string), Value: ParsedCliFile object
cli_file_cache: Dict[str, ParsedCliFile] = {}

# Directory for temporary CLI files (for verification purposes)
TEMP_CLI_DIR = PathLib("temp_cli_files")


def create_temp_cli_file(cli_data: bytes, file_prefix: str = "layer_range") -> PathLib:
    """
    Create a temporary CLI file for verification purposes.

    Args:
        cli_data: The ASCII CLI file data as bytes
        file_prefix: Prefix for the temporary file name

    Returns:
        Path to the created temporary file
    """
    # Ensure temp directory exists
    TEMP_CLI_DIR.mkdir(exist_ok=True)

    # Create temporary file with timestamp
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    temp_filename = f"{file_prefix}_{timestamp}_{uuid.uuid4().hex[:8]}.cli"
    temp_file_path = TEMP_CLI_DIR / temp_filename

    # Write CLI data to file
    with open(temp_file_path, 'wb') as f:
        f.write(cli_data)

    logger.info(f"Created temporary CLI file: {temp_file_path}")
    return temp_file_path


def cleanup_temp_cli_file(file_path: PathLib) -> bool:
    """
    Delete a temporary CLI file.

    Args:
        file_path: Path to the temporary file to delete

    Returns:
        True if file was deleted successfully, False otherwise
    """
    try:
        if file_path.exists():
            file_path.unlink()
            logger.info(f"Deleted temporary CLI file: {file_path}")
            return True
        else:
            logger.warning(f"Temporary CLI file not found for deletion: {file_path}")
            return False
    except Exception as e:
        logger.error(f"Failed to delete temporary CLI file {file_path}: {e}")
        return False


# Pydantic Models for Request/Response Validation

class LayerParametersRequest(BaseModel):
    """Request model for setting layer parameters."""
    filling_id: int = Field(..., description="The ID of the drum with the filling material powder. Set to -1 for no filling.")
    speed: float = Field(..., gt=0, description="The patterning speed [mm/s]")
    powder_saving: bool = Field(True, description="Flag indicating if powder saving strategies are used")
    x_offset: Optional[float] = Field(None, ge=0, description="The offset along the X axis [mm]")


class LayerParametersResponse(BaseModel):
    """Response model for layer parameters."""
    filling_id: int
    speed: float
    powder_saving: bool
    x_offset: Optional[float] = None
    max_x_offset: Optional[float] = None


class PrintJobResponse(BaseModel):
    """Response model for print job operations."""
    success: bool
    status: str
    job_id: Optional[str] = None


class FileUploadResponse(BaseModel):
    """Response model for file upload operations."""
    success: bool
    message: str
    drum_id: int
    file_size: int
    content_type: str


class FileDeleteResponse(BaseModel):
    """Response model for file deletion operations."""
    success: bool
    message: str
    drum_id: int


class PrintJobStatusResponse(BaseModel):
    """Response model for print job status."""
    state: str
    is_printing: bool
    has_error: bool


class CliUploadResponse(BaseModel):
    """Response model for CLI file upload operations."""
    success: bool
    message: str
    file_id: str
    total_layers: int
    file_size: int


class LayerRangeRequest(BaseModel):
    """Request model for selecting a range of layers."""
    start_layer: int = Field(..., ge=1, description="The starting layer number (1-indexed).")
    end_layer: int = Field(..., ge=1, description="The ending layer number (1-indexed).")


class LayerRangeResponse(BaseModel):
    """Response model for sending a layer range."""
    success: bool
    message: str
    file_id: str
    drum_id: int
    layer_range: Tuple[int, int]
    z_height_range: Tuple[float, float]
    data_size: int


class MultiMaterialJobRequest(BaseModel):
    """Request model for starting a multi-material job."""
    file_ids: Dict[int, str] = Field(..., description="Mapping of drum_id (0,1,2) to file_id")


class MultiMaterialJobResponse(BaseModel):
    """Response model for multi-material job operations."""
    success: bool
    message: str
    job_id: Optional[str] = None


class MultiMaterialJobStatusResponse(BaseModel):
    """Response model for multi-material job status."""
    job_id: Optional[str] = None
    is_active: bool
    status: str
    current_layer: int
    total_layers: int
    progress_percentage: float
    error_message: str
    drums: Dict[int, Dict[str, Any]]


class DrumStatusResponse(BaseModel):
    """Response model for individual drum status."""
    drum_id: int
    status: str
    ready: bool
    uploaded: bool
    current_layer: int
    total_layers: int
    error_message: str
    file_id: Optional[str] = None



# API Endpoints

@router.get("/layer/parameters", response_model=LayerParametersResponse)
async def get_layer_parameters(
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> LayerParametersResponse:
    """
    Get the current parameters of the layer.
    
    Returns the current layer parameters including filling drum ID, speed,
    powder saving settings, and X-axis offset.
    """
    try:
        logger.info("Getting layer parameters")
        result = recoater_client.get_layer_parameters()
        
        return LayerParametersResponse(**result)
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting layer parameters: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting layer parameters: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error getting layer parameters: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.put("/layer/parameters")
async def set_layer_parameters(
    parameters: LayerParametersRequest,
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Set the parameters of the current layer.
    
    Defines the parameters for the current layer including which drum contains
    the filling material, patterning speed, and powder saving settings.
    """
    try:
        logger.info(f"Setting layer parameters: {parameters.model_dump()}")
        
        result = recoater_client.set_layer_parameters(
            filling_id=parameters.filling_id,
            speed=parameters.speed,
            powder_saving=parameters.powder_saving,
            x_offset=parameters.x_offset
        )
        
        return {
            "success": True,
            "message": "Layer parameters set successfully",
            "parameters": parameters.model_dump()
        }
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting layer parameters: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error setting layer parameters: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error setting layer parameters: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.get("/layer/preview")
async def get_layer_preview(
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> Response:
    """
    Get layer preview as PNG image.

    Returns a PNG image preview of the layer showing the powder allocation.
    """
    try:
        logger.info("Getting layer preview")
        image_data = recoater_client.get_layer_preview()

        return Response(
            content=image_data,
            media_type="image/png",
            headers={"Content-Disposition": "inline; filename=layer_preview.png"}
        )

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting layer preview: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting layer preview: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error getting layer preview: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.get("/drums/{drum_id}/geometry/preview")
async def get_drum_geometry_preview(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> Response:
    """
    Get geometry file from a specific drum as PNG preview.

    This endpoint retrieves the stored geometry file from the specified drum
    and returns it as a PNG image for preview purposes. This is different from
    the general layer preview which shows the current layer configuration.
    """
    try:
        logger.info(f"Getting geometry preview from drum {drum_id}")

        # Download geometry from recoater
        image_data = recoater_client.download_drum_geometry(drum_id)

        return Response(
            content=image_data,
            media_type="image/png",
            headers={"Content-Disposition": f"inline; filename=drum_{drum_id}_preview.png"}
        )

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting geometry preview from drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting geometry preview from drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error getting geometry preview from drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.post("/job", response_model=PrintJobResponse)
async def start_print_job(
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> PrintJobResponse:
    """
    Create a printing job if the server is ready to start.
    
    Creates a printing job and starts the printing procedure. The recoater
    will wait for the synchronization signal.
    """
    try:
        logger.info("Starting print job")
        result = recoater_client.start_print_job()
        
        return PrintJobResponse(
            success=True,
            status="started",
            job_id=result.get("job_id")
        )
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error starting print job: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error starting print job: {e}")
        raise HTTPException(status_code=409, detail=f"Cannot start print job: {e}")
    except Exception as e:
        logger.error(f"Unexpected error starting print job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.delete("/job", response_model=PrintJobResponse)
async def cancel_print_job(
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> PrintJobResponse:
    """
    Cancel and remove the current printing job.
    
    Cancels and removes the current printing job if one is running.
    """
    try:
        logger.info("Cancelling print job")
        result = recoater_client.cancel_print_job()
        
        return PrintJobResponse(
            success=True,
            status="cancelled"
        )
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error cancelling print job: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error cancelling print job: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error cancelling print job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.get("/job/status", response_model=PrintJobStatusResponse)
async def get_print_job_status(
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> PrintJobStatusResponse:
    """
    Get the current print job status.

    Returns the current state of the print job including whether it's printing,
    has errors, or is ready for new jobs.
    """
    try:
        logger.info("Getting print job status")
        result = recoater_client.get_print_job_status()

        return PrintJobStatusResponse(**result)

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting print job status: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting print job status: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error getting print job status: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


# CLI File Management Endpoints

@router.post("/cli/upload", response_model=CliUploadResponse)
async def upload_cli_file(
    file: UploadFile = File(...),
    job_manager: MultiMaterialJobManager = Depends(get_multilayer_job_manager)
) -> CliUploadResponse:
    """
    Upload and parse a multi-layer CLI file.

    This endpoint receives a CLI file from the frontend, parses it using the
    CliParserService, and stores the parsed data in memory. Returns a unique
    file ID and the total number of layers found.
    """
    try:
        logger.info(f"Uploading CLI file: {file.filename}")

        # Validate file type
        if not file.filename or not file.filename.lower().endswith('.cli'):
            raise HTTPException(status_code=400, detail="Only .cli files are supported")

        # Read file data
        file_data = await file.read()

        if len(file_data) == 0:
            raise HTTPException(status_code=400, detail="Uploaded file is empty")

        # Parse CLI file
        parser = CliParserService(logger=logger)
        parsed_data = parser.parse(file_data)

        # Generate unique file ID and store in cache
        file_id = str(uuid.uuid4())
        cli_file_cache[file_id] = parsed_data

        # Also add to job manager cache for multi-material jobs
        job_manager.add_cli_file(file_id, parsed_data)

        logger.info(f"CLI file parsed successfully. File ID: {file_id}, Layers: {len(parsed_data.layers)}")

        return CliUploadResponse(
            success=True,
            message=f"CLI file {file.filename} uploaded and parsed successfully",
            file_id=file_id,
            total_layers=len(parsed_data.layers),
            file_size=len(file_data)
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except CliParsingError as e:
        logger.error(f"CLI parsing error: {e}")
        raise HTTPException(status_code=400, detail=f"CLI parsing error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error uploading CLI file: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.get("/cli/{file_id}/layer/{layer_num}/preview")
async def get_cli_layer_preview(
    file_id: str = Path(..., description="The unique ID of the uploaded CLI file"),
    layer_num: int = Path(..., ge=1, description="The layer number to preview (1-based)")
) -> Response:
    """
    Get a preview of a specific layer from a parsed CLI file as PNG image.

    This endpoint retrieves the cached ParsedCliFile object, selects the requested
    layer, and renders it as a PNG image using the CliParserService.
    """
    try:
        logger.info(f"Getting CLI layer preview for file {file_id}, layer {layer_num}")

        # Check if file exists in cache
        if file_id not in cli_file_cache:
            raise HTTPException(status_code=404, detail="CLI file not found. Please upload the file first.")

        parsed_data = cli_file_cache[file_id]

        # Validate layer number (convert from 1-based to 0-based index)
        layer_index = layer_num - 1
        if layer_index < 0 or layer_index >= len(parsed_data.layers):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid layer number. Must be between 1 and {len(parsed_data.layers)}"
            )

        # Get the specific layer
        layer = parsed_data.layers[layer_index]

        # Render layer to PNG
        parser = CliParserService(logger=logger)
        png_bytes = parser.render_layer_to_png(layer)

        logger.info(f"CLI layer {layer_num} rendered successfully for file {file_id}")

        return Response(
            content=png_bytes,
            media_type="image/png",
            headers={"Content-Disposition": f"inline; filename=cli_layer_{layer_num}_preview.png"}
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting CLI layer preview: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.post("/cli/{file_id}/layer/{layer_num}/send/{drum_id}")
async def send_cli_layer_to_drum(
    file_id: str = Path(..., description="The unique ID of the uploaded CLI file"),
    layer_num: int = Path(..., ge=1, description="The layer number to send (1-based)"),
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2) to send the layer to"),
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Send a specific layer from a parsed CLI file to a drum for printing.

    This endpoint retrieves the cached ParsedCliFile object, extracts the requested
    layer, generates CLI data for that single layer, and uploads it to the specified drum.
    """
    try:
        logger.info(f"Sending CLI layer {layer_num} from file {file_id} to drum {drum_id}")

        # Check if file exists in cache
        if file_id not in cli_file_cache:
            raise HTTPException(status_code=404, detail="CLI file not found. Please upload the file first.")

        parsed_data = cli_file_cache[file_id]

        # Validate layer number (convert from 1-based to 0-based index)
        layer_index = layer_num - 1
        if layer_index < 0 or layer_index >= len(parsed_data.layers):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid layer number. Must be between 1 and {len(parsed_data.layers)}"
            )

        # Get the specific layer
        layer = parsed_data.layers[layer_index]

        # Generate ASCII CLI data for the single layer (hardware requires ASCII format)
        parser = CliParserService(logger=logger)
        cli_data = parser.generate_single_layer_ascii_cli(
            layer=layer,
            header_lines=parsed_data.header_lines
        )

        # --- Create Temporary CLI File for Verification ---
        temp_file_path = create_temp_cli_file(
            cli_data,
            f"layer_{layer_num}_drum_{drum_id}"
        )

        # Upload the single layer CLI data to the specified drum
        try:
            result = recoater_client.upload_drum_geometry(
                drum_id=drum_id,
                file_data=cli_data,
                content_type="application/octet-stream"
            )

            # Clean up temporary file after successful upload
            cleanup_temp_cli_file(temp_file_path)

        except Exception as upload_error:
            logger.error(f"Upload failed with error: {upload_error}")
            # Keep temporary file for debugging if upload fails
            logger.info(f"Temporary CLI file preserved for debugging: {temp_file_path}")
            raise

        logger.info(f"CLI layer {layer_num} sent successfully to drum {drum_id}")

        return {
            "success": True,
            "message": f"Layer {layer_num} from CLI file sent successfully to drum {drum_id}",
            "file_id": file_id,
            "layer_num": layer_num,
            "drum_id": drum_id,
            "layer_z_height": layer.z_height,
            "data_size": len(cli_data)
        }

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except CliParsingError as e:
        logger.error(f"CLI generation error: {e}")
        raise HTTPException(status_code=400, detail=f"CLI generation error: {e}")
    except RecoaterConnectionError as e:
        logger.error(f"Connection error sending layer to drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error sending layer to drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error sending CLI layer to drum: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.post("/cli/{file_id}/layers/send/{drum_id}", response_model=LayerRangeResponse)
async def send_cli_layer_range_to_drum(
    file_id: str = Path(..., description="The unique ID of the uploaded CLI file"),
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2) to send the layer range to"),
    layer_range: LayerRangeRequest = Body(...),
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> LayerRangeResponse:
    """
    Send a range of layers from a parsed CLI file to a drum for printing.

    This endpoint extracts a specified range of layers, generates a new CLI file
    from them, and uploads it to the specified drum.
    """
    try:
        start_layer, end_layer = layer_range.start_layer, layer_range.end_layer
        logger.info(f"Sending CLI layer range {start_layer}-{end_layer} from file {file_id} to drum {drum_id}")

        # --- Validation ---
        if file_id not in cli_file_cache:
            raise HTTPException(status_code=404, detail="CLI file not found. Please upload the file first.")

        if start_layer > end_layer:
            raise HTTPException(status_code=400, detail="Start layer cannot be greater than end layer.")

        parsed_data = cli_file_cache[file_id]
        total_layers = len(parsed_data.layers)

        if not (1 <= start_layer <= total_layers and 1 <= end_layer <= total_layers):
            raise HTTPException(status_code=400, detail=f"Invalid layer range. Must be between 1 and {total_layers}.")

        # --- Layer Extraction and CLI Generation ---
        layer_indices = range(start_layer - 1, end_layer) # Convert to 0-based index
        selected_layers = [parsed_data.layers[i] for i in layer_indices]

        if not selected_layers:
            raise HTTPException(status_code=400, detail="The selected layer range is empty.")

        parser = CliParserService(logger=logger)
        cli_data = parser.generate_ascii_cli_from_layer_range(
            layers=selected_layers,
            header_lines=parsed_data.header_lines
        )

        # --- Create Temporary CLI File for Verification ---
        temp_file_path = create_temp_cli_file(
            cli_data,
            f"layers_{start_layer}-{end_layer}_drum_{drum_id}"
        )

        # --- Upload to Recoater ---
        try:
            logger.info(f"Attempting to upload CLI data to drum {drum_id}")
            upload_result = recoater_client.upload_drum_geometry(
                drum_id=drum_id,
                file_data=cli_data,
                content_type="application/octet-stream"
            )
            logger.info(f"Upload result: {upload_result}")

            # Clean up temporary file after successful upload
            cleanup_temp_cli_file(temp_file_path)

        except Exception as upload_error:
            logger.error(f"Upload failed with error: {upload_error}")
            # Keep temporary file for debugging if upload fails
            logger.info(f"Temporary CLI file preserved for debugging: {temp_file_path}")
            raise

        logger.info(f"CLI layer range {start_layer}-{end_layer} sent successfully to drum {drum_id}")

        z_heights = [layer.z_height for layer in selected_layers]

        return LayerRangeResponse(
            success=True,
            message=f"Layer range {start_layer}-{end_layer} from CLI file sent successfully to drum {drum_id}",
            file_id=file_id,
            drum_id=drum_id,
            layer_range=(start_layer, end_layer),
            z_height_range=(min(z_heights), max(z_heights)),
            data_size=len(cli_data)
        )

    except HTTPException:
        raise
    except CliParsingError as e:
        logger.error(f"CLI generation error for layer range: {e}")
        raise HTTPException(status_code=400, detail=f"CLI generation error: {e}")
    except RecoaterConnectionError as e:
        logger.error(f"Connection error sending layer range to drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error sending layer range to drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error sending CLI layer range to drum: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


# File Management Endpoints

@router.post("/drums/{drum_id}/geometry", response_model=FileUploadResponse)
async def upload_drum_geometry(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    file: UploadFile = File(...),
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> FileUploadResponse:
    """
    Upload geometry file (PNG or CLI) to a specific drum.

    Uploads a geometry file to the specified drum. The file can be either
    a PNG image or a CLI file containing layer geometry data.
    """
    try:
        logger.info(f"Uploading geometry file to drum {drum_id}: {file.filename}")

        # Read file data
        file_data = await file.read()

        # Upload to recoater
        result = recoater_client.upload_drum_geometry(
            drum_id=drum_id,
            file_data=file_data,
            content_type=file.content_type or "application/octet-stream"
        )

        return FileUploadResponse(
            success=True,
            message=f"File {file.filename} uploaded successfully to drum {drum_id}",
            drum_id=drum_id,
            file_size=len(file_data),
            content_type=file.content_type or "application/octet-stream"
        )

    except RecoaterConnectionError as e:
        logger.error(f"Connection error uploading file to drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error uploading file to drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error uploading file to drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.get("/drums/{drum_id}/geometry")
async def download_drum_geometry(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> Response:
    """
    Download geometry file from a specific drum as PNG image.

    Downloads the geometry file from the specified drum and returns it
    as a PNG image.
    """
    try:
        logger.info(f"Downloading geometry file from drum {drum_id}")

        # Download from recoater
        image_data = recoater_client.download_drum_geometry(drum_id)

        return Response(
            content=image_data,
            media_type="image/png",
            headers={"Content-Disposition": f"attachment; filename=drum_{drum_id}_geometry.png"}
        )

    except RecoaterConnectionError as e:
        logger.error(f"Connection error downloading file from drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error downloading file from drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error downloading file from drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.delete("/drums/{drum_id}/geometry", response_model=FileDeleteResponse)
async def delete_drum_geometry(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> FileDeleteResponse:
    """
    Delete geometry file from a specific drum.

    Removes the geometry file from the specified drum.
    """
    try:
        logger.info(f"Deleting geometry file from drum {drum_id}")

        # Delete from recoater
        result = recoater_client.delete_drum_geometry(drum_id)

        return FileDeleteResponse(
            success=True,
            message=f"Geometry file deleted successfully from drum {drum_id}",
            drum_id=drum_id
        )

    except RecoaterConnectionError as e:
        logger.error(f"Connection error deleting file from drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error deleting file from drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error deleting file from drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


# Multi-Material Job Management Endpoints

@router.post("/cli/start-multimaterial-job", response_model=MultiMaterialJobResponse)
async def start_multimaterial_job(
    job_request: MultiMaterialJobRequest,
    job_manager: MultiMaterialJobManager = Depends(get_multilayer_job_manager)
) -> MultiMaterialJobResponse:
    """
    Start a multi-material print job with 3 CLI files.

    This endpoint creates and starts a multi-material job that coordinates
    printing across 3 drums using the provided CLI files.
    """
    try:
        # Validate file_ids mapping
        if len(job_request.file_ids) != 3:
            raise HTTPException(
                status_code=400,
                detail="Must provide exactly 3 CLI files for drums 0, 1, 2"
            )

        for drum_id in [0, 1, 2]:
            if drum_id not in job_request.file_ids:
                raise HTTPException(
                    status_code=400,
                    detail=f"Missing CLI file for drum {drum_id}"
                )

        # Create the job
        job_state = await job_manager.create_job(job_request.file_ids)

        # Start the job
        started = await job_manager.start_job()

        if started:
            return MultiMaterialJobResponse(
                success=True,
                message="Multi-material job started successfully",
                job_id=job_state.job_id
            )
        else:
            return MultiMaterialJobResponse(
                success=False,
                message="Failed to start multi-material job"
            )

    except MultiMaterialJobError as e:
        logger.error(f"Multi-material job error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error starting multi-material job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.get("/multimaterial-job/status", response_model=MultiMaterialJobStatusResponse)
async def get_multimaterial_job_status(
    job_manager: MultiMaterialJobManager = Depends(get_multilayer_job_manager)
) -> MultiMaterialJobStatusResponse:
    """
    Get the current status of the active multi-material job.
    """
    try:
        status = job_manager.get_job_status()

        if status is None:
            return MultiMaterialJobStatusResponse(
                is_active=False,
                status="idle",
                current_layer=0,
                total_layers=0,
                progress_percentage=0.0,
                error_message="",
                drums={}
            )

        return MultiMaterialJobStatusResponse(**status)

    except Exception as e:
        logger.error(f"Unexpected error getting multi-material job status: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.post("/multimaterial-job/cancel", response_model=MultiMaterialJobResponse)
async def cancel_multimaterial_job(
    job_manager: MultiMaterialJobManager = Depends(get_multilayer_job_manager)
) -> MultiMaterialJobResponse:
    """
    Cancel the active multi-material job.
    """
    try:
        cancelled = await job_manager.cancel_job()

        if cancelled:
            return MultiMaterialJobResponse(
                success=True,
                message="Multi-material job cancelled successfully"
            )
        else:
            return MultiMaterialJobResponse(
                success=False,
                message="Failed to cancel multi-material job"
            )

    except Exception as e:
        logger.error(f"Unexpected error cancelling multi-material job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.post("/multimaterial-job/clear-error", response_model=MultiMaterialJobResponse)
async def clear_multimaterial_job_errors(
    job_manager: MultiMaterialJobManager = Depends(get_multilayer_job_manager)
) -> MultiMaterialJobResponse:
    """
    Clear error flags for operator error recovery.
    """
    try:
        cleared = await job_manager.clear_error_flags()

        if cleared:
            return MultiMaterialJobResponse(
                success=True,
                message="Error flags cleared successfully"
            )
        else:
            return MultiMaterialJobResponse(
                success=False,
                message="Failed to clear error flags"
            )

    except Exception as e:
        logger.error(f"Unexpected error clearing error flags: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.get("/multimaterial-job/drum-status/{drum_id}", response_model=DrumStatusResponse)
async def get_drum_status(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    job_manager: MultiMaterialJobManager = Depends(get_multilayer_job_manager)
) -> DrumStatusResponse:
    """
    Get status for a specific drum in the multi-material job.
    """
    try:
        status = job_manager.get_drum_status(drum_id)

        if status is None:
            raise HTTPException(
                status_code=404,
                detail=f"No active job or drum {drum_id} not found"
            )

        return DrumStatusResponse(**status)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting drum {drum_id} status: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")
