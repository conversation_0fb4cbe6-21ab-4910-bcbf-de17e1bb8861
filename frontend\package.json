{"name": "recoater-hmi-frontend", "version": "1.0.0", "description": "Frontend for Recoater Custom HMI", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"axios": "^1.6.2", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@playwright/test": "^1.55.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/test-utils": "^2.4.2", "eslint": "^8.53.0", "jsdom": "^23.0.1", "prettier": "^3.1.0", "vite": "^5.0.0", "vitest": "^1.0.0"}}